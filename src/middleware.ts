import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { isAuthenticated, createMockCloudronHeaders } from './lib/auth'

export function middleware(request: NextRequest) {
  // En développement, ajouter les headers mock Cloudron
  if (process.env.NODE_ENV === 'development') {
    const mockHeaders = createMockCloudronHeaders()
    const requestHeaders = new Headers(request.headers)
    
    Object.entries(mockHeaders).forEach(([key, value]) => {
      requestHeaders.set(key, value)
    })

    const response = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    })
    
    return response
  }

  // En production, vérifier l'authentification Cloudron
  if (!isAuthenticated(request)) {
    // Rediriger vers la page de connexion Cloudron ou afficher une erreur
    return new NextResponse('Non autorisé', { status: 401 })
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
