'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Play,
  Square,
  Tag,
  DollarSign,
  MoreHorizontal,
  Plus,
  Circle
} from 'lucide-react'

// Types pour les données de l'API
interface Project {
  id: number
  name: string
  clientName: string
  color?: string
}

interface TimeEntry {
  id: number
  projectId: number
  projectName: string
  clientName: string
  description: string
  startTime: string
  endTime?: string
  duration: number
  isRunning: boolean
  isBillable?: boolean
}

// Couleurs par défaut pour les projets
const projectColors = [
  '#ef4444', '#3b82f6', '#10b981', '#f59e0b',
  '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
]

function getProjectColor(projectId: number): string {
  return projectColors[projectId % projectColors.length]
}

function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

function TimeEntryRow({ entry, onStop }: { entry: TimeEntry; onStop: (id: number) => void }) {
  const [currentDuration, setCurrentDuration] = useState(entry.duration || 0)

  useEffect(() => {
    if (entry.isRunning) {
      const interval = setInterval(() => {
        const startTime = new Date(entry.startTime).getTime()
        const now = Date.now()
        const newDuration = Math.floor((now - startTime) / 1000)
        setCurrentDuration(newDuration)
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [entry.isRunning, entry.startTime])

  const handleStopTimer = () => {
    if (entry.isRunning) {
      onStop(entry.id)
    }
  }

  return (
    <Card className="p-4">
      <div className="flex items-center gap-4">
        {/* Indicateur de couleur du projet */}
        <div
          className="w-3 h-3 rounded-full flex-shrink-0"
          style={{ backgroundColor: getProjectColor(entry.projectId) }}
        />

        {/* Nom du projet et client */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">
              {entry.projectName}
            </span>
            <span className="text-xs text-muted-foreground">
              - {entry.clientName}
            </span>
          </div>
          <Input
            placeholder="Ajouter une description..."
            defaultValue={entry.description}
            className="mt-1 border-none p-0 h-auto text-sm bg-transparent focus-visible:ring-0"
          />
        </div>

        {/* Icône billable */}
        <Button
          variant="ghost"
          size="sm"
          className={`p-1 h-8 w-8 ${entry.isBillable ? 'text-green-600' : 'text-muted-foreground'}`}
        >
          <Tag className="h-4 w-4" />
        </Button>

        {/* Icône dollar (pour les taux) */}
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-8 w-8 text-muted-foreground"
        >
          <DollarSign className="h-4 w-4" />
        </Button>

        {/* Durée */}
        <div className="text-lg font-mono font-medium min-w-[80px] text-right">
          {formatDuration(currentDuration)}
        </div>

        {/* Bouton Start/Stop */}
        <Button
          variant={entry.isRunning ? "destructive" : "default"}
          size="sm"
          className="min-w-[70px]"
          onClick={handleStopTimer}
        >
          {entry.isRunning ? (
            <>
              <Square className="h-4 w-4 mr-1" />
              STOP
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-1" />
              START
            </>
          )}
        </Button>

        {/* Menu options */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>Modifier</DropdownMenuItem>
            <DropdownMenuItem>Dupliquer</DropdownMenuItem>
            <DropdownMenuItem className="text-destructive">
              Supprimer
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </Card>
  )
}

function NewTimeEntryRow({ projects, onStart }: { projects: Project[]; onStart: (projectId: number, description: string) => void }) {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [description, setDescription] = useState('')

  const handleStart = () => {
    if (selectedProject) {
      onStart(selectedProject.id, description)
      setDescription('')
    }
  }

  return (
    <Card className="p-4 border-dashed">
      <div className="flex items-center gap-4">
        {/* Sélecteur de projet */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2 h-8">
              {selectedProject ? (
                <>
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: getProjectColor(selectedProject.id) }}
                  />
                  <span className="text-sm">
                    {selectedProject.name} - {selectedProject.clientName}
                  </span>
                </>
              ) : (
                <>
                  <Circle className="h-3 w-3" />
                  <span className="text-sm text-muted-foreground">
                    Choisir un projet
                  </span>
                </>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64">
            {projects.map((project) => (
              <DropdownMenuItem
                key={project.id}
                onClick={() => setSelectedProject(project)}
                className="flex items-center gap-2"
              >
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: getProjectColor(project.id) }}
                />
                <span>{project.name} - {project.clientName}</span>
              </DropdownMenuItem>
            ))}
            <DropdownMenuItem className="flex items-center gap-2 text-blue-600">
              <Plus className="h-3 w-3" />
              <span>Nouveau projet</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Description */}
        <div className="flex-1">
          <Input
            placeholder="Que faites-vous ?"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            className="border-none p-0 h-auto text-sm bg-transparent focus-visible:ring-0"
          />
        </div>

        {/* Icônes et bouton start */}
        <Button variant="ghost" size="sm" className="p-1 h-8 w-8 text-muted-foreground">
          <Tag className="h-4 w-4" />
        </Button>

        <Button variant="ghost" size="sm" className="p-1 h-8 w-8 text-muted-foreground">
          <DollarSign className="h-4 w-4" />
        </Button>

        <div className="text-lg font-mono font-medium min-w-[80px] text-right text-muted-foreground">
          00:00:00
        </div>

        <Button
          disabled={!selectedProject}
          size="sm"
          className="min-w-[70px]"
          onClick={handleStart}
        >
          <Play className="h-4 w-4 mr-1" />
          START
        </Button>

        <div className="w-8" /> {/* Espace pour aligner avec les autres lignes */}
      </div>
    </Card>
  )
}

export function TimeTracker() {
  const [projects, setProjects] = useState<Project[]>([])
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([])
  const [loading, setLoading] = useState(true)

  // Charger les projets et entrées de temps
  useEffect(() => {
    const loadData = async () => {
      try {
        // Charger les projets
        const projectsRes = await fetch('/api/projects')
        if (projectsRes.ok) {
          const projectsData = await projectsRes.json()
          setProjects(projectsData.projects || [])
        }

        // Charger les entrées de temps
        const entriesRes = await fetch('/api/time-entries')
        if (entriesRes.ok) {
          const entriesData = await entriesRes.json()
          setTimeEntries(entriesData.timeEntries || [])
        }
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const handleStartTimer = async (projectId: number, description: string) => {
    try {
      const response = await fetch('/api/time-entries/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId, description })
      })

      if (response.ok) {
        const data = await response.json()
        setTimeEntries(prev => [data.timeEntry, ...prev])
      }
    } catch (error) {
      console.error('Erreur lors du démarrage du timer:', error)
    }
  }

  const handleStopTimer = async (entryId: number) => {
    try {
      const response = await fetch(`/api/time-entries/${entryId}/stop`, {
        method: 'POST'
      })

      if (response.ok) {
        const data = await response.json()
        setTimeEntries(prev =>
          prev.map(entry =>
            entry.id === entryId ? data.timeEntry : entry
          )
        )
      }
    } catch (error) {
      console.error('Erreur lors de l\'arrêt du timer:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Card className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-muted rounded w-1/4 mb-2"></div>
            <div className="h-8 bg-muted rounded"></div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Nouvelle entrée */}
      <NewTimeEntryRow projects={projects} onStart={handleStartTimer} />

      {/* Entrées existantes */}
      {timeEntries.map((entry) => (
        <TimeEntryRow key={entry.id} entry={entry} onStop={handleStopTimer} />
      ))}

      {/* Message si aucune entrée */}
      {timeEntries.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <p>Aucune entrée de temps aujourd'hui.</p>
          <p className="text-sm">Commencez par sélectionner un projet et démarrer le timer !</p>
        </div>
      )}
    </div>
  )
}
