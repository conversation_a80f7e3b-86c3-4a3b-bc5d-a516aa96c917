import sqlite3 from 'sqlite3'
import { promisify } from 'util'
import path from 'path'

// Types pour notre base de données
export interface Client {
  id: number
  name: string
  email?: string
  hourlyRate?: number
  createdAt: string
  updatedAt: string
}

export interface Project {
  id: number
  name: string
  description?: string
  clientId: number
  hourlyRate?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface TimeEntry {
  id: number
  projectId: number
  description?: string
  startTime: string
  endTime?: string
  duration?: number // en minutes
  isRunning: boolean
  userId: string
  createdAt: string
  updatedAt: string
}

class Database {
  private db: sqlite3.Database | null = null

  async connect() {
    if (this.db) return this.db

    const dbPath = path.join(process.cwd(), 'data', 'trackify.db')
    
    // Créer le dossier data s'il n'existe pas
    const fs = require('fs')
    const dataDir = path.dirname(dbPath)
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
    }

    this.db = new sqlite3.Database(dbPath)
    
    // Promisifier les méthodes
    const run = promisify(this.db.run.bind(this.db))
    const get = promisify(this.db.get.bind(this.db))
    const all = promisify(this.db.all.bind(this.db))

    // Créer les tables
    await this.createTables()
    
    return this.db
  }

  private async createTables() {
    if (!this.db) throw new Error('Database not connected')

    const run = promisify(this.db.run.bind(this.db))

    // Table clients
    await run(`
      CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        hourlyRate REAL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Table projets
    await run(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        clientId INTEGER NOT NULL,
        hourlyRate REAL,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clientId) REFERENCES clients (id)
      )
    `)

    // Table entrées de temps
    await run(`
      CREATE TABLE IF NOT EXISTS timeEntries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectId INTEGER NOT NULL,
        description TEXT,
        startTime DATETIME NOT NULL,
        endTime DATETIME,
        duration INTEGER,
        isRunning BOOLEAN DEFAULT 0,
        userId TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (projectId) REFERENCES projects (id)
      )
    `)

    // Index pour les performances
    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_userId ON timeEntries (userId)`)
    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_projectId ON timeEntries (projectId)`)
    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_startTime ON timeEntries (startTime)`)
  }

  async query(sql: string, params: any[] = []): Promise<any[]> {
    await this.connect()
    if (!this.db) throw new Error('Database not connected')

    const all = promisify(this.db.all.bind(this.db))
    return all(sql, params)
  }

  async get(sql: string, params: any[] = []): Promise<any> {
    await this.connect()
    if (!this.db) throw new Error('Database not connected')

    const get = promisify(this.db.get.bind(this.db))
    return get(sql, params)
  }

  async run(sql: string, params: any[] = []): Promise<{ lastID: number; changes: number }> {
    await this.connect()
    if (!this.db) throw new Error('Database not connected')

    const run = promisify(this.db.run.bind(this.db))
    return run(sql, params)
  }

  async close() {
    if (this.db) {
      const close = promisify(this.db.close.bind(this.db))
      await close()
      this.db = null
    }
  }
}

// Instance singleton
export const db = new Database()
