import { db, Project } from '../database'

export class ProjectService {
  static async getAll(): Promise<Project[]> {
    return db.query(`
      SELECT p.*, c.name as clientName 
      FROM projects p 
      LEFT JOIN clients c ON p.clientId = c.id 
      ORDER BY p.name
    `)
  }

  static async getById(id: number): Promise<Project | null> {
    return db.get(`
      SELECT p.*, c.name as clientName 
      FROM projects p 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE p.id = ?
    `, [id])
  }

  static async getByClientId(clientId: number): Promise<Project[]> {
    return db.query(`
      SELECT p.*, c.name as clientName 
      FROM projects p 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE p.clientId = ? 
      ORDER BY p.name
    `, [clientId])
  }

  static async getActive(): Promise<Project[]> {
    return db.query(`
      SELECT p.*, c.name as clientName 
      FROM projects p 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE p.isActive = 1 
      ORDER BY p.name
    `)
  }

  static async create(data: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {
    const result = await db.run(
      'INSERT INTO projects (name, description, clientId, hourlyRate, isActive) VALUES (?, ?, ?, ?, ?)',
      [data.name, data.description, data.clientId, data.hourlyRate, data.isActive ? 1 : 0]
    )
    
    const project = await this.getById(result.lastID)
    if (!project) throw new Error('Failed to create project')
    return project
  }

  static async update(id: number, data: Partial<Omit<Project, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Project> {
    const updates: string[] = []
    const values: any[] = []

    if (data.name !== undefined) {
      updates.push('name = ?')
      values.push(data.name)
    }
    if (data.description !== undefined) {
      updates.push('description = ?')
      values.push(data.description)
    }
    if (data.clientId !== undefined) {
      updates.push('clientId = ?')
      values.push(data.clientId)
    }
    if (data.hourlyRate !== undefined) {
      updates.push('hourlyRate = ?')
      values.push(data.hourlyRate)
    }
    if (data.isActive !== undefined) {
      updates.push('isActive = ?')
      values.push(data.isActive ? 1 : 0)
    }

    if (updates.length === 0) {
      const project = await this.getById(id)
      if (!project) throw new Error('Project not found')
      return project
    }

    updates.push('updatedAt = CURRENT_TIMESTAMP')
    values.push(id)

    await db.run(
      `UPDATE projects SET ${updates.join(', ')} WHERE id = ?`,
      values
    )

    const project = await this.getById(id)
    if (!project) throw new Error('Project not found')
    return project
  }

  static async delete(id: number): Promise<void> {
    await db.run('DELETE FROM projects WHERE id = ?', [id])
  }
}
