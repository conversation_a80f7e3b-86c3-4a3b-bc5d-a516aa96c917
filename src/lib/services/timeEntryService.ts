import { db, TimeEntry } from '../database'

export class TimeEntryService {
  static async getAll(userId: string): Promise<TimeEntry[]> {
    return db.query(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.userId = ? 
      ORDER BY t.startTime DESC
    `, [userId])
  }

  static async getById(id: number, userId: string): Promise<TimeEntry | null> {
    return db.get(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.id = ? AND t.userId = ?
    `, [id, userId])
  }

  static async getRunning(userId: string): Promise<TimeEntry | null> {
    return db.get(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.userId = ? AND t.isRunning = 1
    `, [userId])
  }

  static async getByDateRange(userId: string, startDate: string, endDate: string): Promise<TimeEntry[]> {
    return db.query(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.userId = ? AND DATE(t.startTime) BETWEEN ? AND ? 
      ORDER BY t.startTime DESC
    `, [userId, startDate, endDate])
  }

  static async create(data: Omit<TimeEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<TimeEntry> {
    const result = await db.run(
      'INSERT INTO timeEntries (projectId, description, startTime, endTime, duration, isRunning, userId) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [data.projectId, data.description, data.startTime, data.endTime, data.duration, data.isRunning ? 1 : 0, data.userId]
    )
    
    const timeEntry = await this.getById(result.lastID, data.userId)
    if (!timeEntry) throw new Error('Failed to create time entry')
    return timeEntry
  }

  static async startTimer(projectId: number, userId: string, description?: string): Promise<TimeEntry> {
    // Arrêter tout timer en cours
    await this.stopAllRunningTimers(userId)

    const startTime = new Date().toISOString()
    return this.create({
      projectId,
      description,
      startTime,
      isRunning: true,
      userId
    })
  }

  static async stopTimer(id: number, userId: string): Promise<TimeEntry> {
    const timeEntry = await this.getById(id, userId)
    if (!timeEntry) throw new Error('Time entry not found')
    if (!timeEntry.isRunning) throw new Error('Timer is not running')

    const endTime = new Date().toISOString()
    const startTime = new Date(timeEntry.startTime)
    const duration = Math.floor((new Date(endTime).getTime() - startTime.getTime()) / 1000 / 60) // en minutes

    await db.run(
      'UPDATE timeEntries SET endTime = ?, duration = ?, isRunning = 0, updatedAt = CURRENT_TIMESTAMP WHERE id = ? AND userId = ?',
      [endTime, duration, id, userId]
    )

    const updatedEntry = await this.getById(id, userId)
    if (!updatedEntry) throw new Error('Failed to update time entry')
    return updatedEntry
  }

  static async stopAllRunningTimers(userId: string): Promise<void> {
    const runningEntries = await db.query(
      'SELECT * FROM timeEntries WHERE userId = ? AND isRunning = 1',
      [userId]
    )

    for (const entry of runningEntries) {
      await this.stopTimer(entry.id, userId)
    }
  }

  static async update(id: number, userId: string, data: Partial<Omit<TimeEntry, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>): Promise<TimeEntry> {
    const updates: string[] = []
    const values: any[] = []

    if (data.projectId !== undefined) {
      updates.push('projectId = ?')
      values.push(data.projectId)
    }
    if (data.description !== undefined) {
      updates.push('description = ?')
      values.push(data.description)
    }
    if (data.startTime !== undefined) {
      updates.push('startTime = ?')
      values.push(data.startTime)
    }
    if (data.endTime !== undefined) {
      updates.push('endTime = ?')
      values.push(data.endTime)
    }
    if (data.duration !== undefined) {
      updates.push('duration = ?')
      values.push(data.duration)
    }

    if (updates.length === 0) {
      const timeEntry = await this.getById(id, userId)
      if (!timeEntry) throw new Error('Time entry not found')
      return timeEntry
    }

    updates.push('updatedAt = CURRENT_TIMESTAMP')
    values.push(id, userId)

    await db.run(
      `UPDATE timeEntries SET ${updates.join(', ')} WHERE id = ? AND userId = ?`,
      values
    )

    const timeEntry = await this.getById(id, userId)
    if (!timeEntry) throw new Error('Time entry not found')
    return timeEntry
  }

  static async delete(id: number, userId: string): Promise<void> {
    await db.run('DELETE FROM timeEntries WHERE id = ? AND userId = ?', [id, userId])
  }
}
