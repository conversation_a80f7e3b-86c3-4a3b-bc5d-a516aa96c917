import { db, Client } from '../database'

export class ClientService {
  static async getAll(): Promise<Client[]> {
    return db.query('SELECT * FROM clients ORDER BY name')
  }

  static async getById(id: number): Promise<Client | null> {
    return db.get('SELECT * FROM clients WHERE id = ?', [id])
  }

  static async create(data: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>): Promise<Client> {
    const result = await db.run(
      'INSERT INTO clients (name, email, hourlyRate) VALUES (?, ?, ?)',
      [data.name, data.email, data.hourlyRate]
    )
    
    const client = await this.getById(result.lastID)
    if (!client) throw new Error('Failed to create client')
    return client
  }

  static async update(id: number, data: Partial<Omit<Client, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Client> {
    const updates: string[] = []
    const values: any[] = []

    if (data.name !== undefined) {
      updates.push('name = ?')
      values.push(data.name)
    }
    if (data.email !== undefined) {
      updates.push('email = ?')
      values.push(data.email)
    }
    if (data.hourlyRate !== undefined) {
      updates.push('hourlyRate = ?')
      values.push(data.hourlyRate)
    }

    if (updates.length === 0) {
      const client = await this.getById(id)
      if (!client) throw new Error('Client not found')
      return client
    }

    updates.push('updatedAt = CURRENT_TIMESTAMP')
    values.push(id)

    await db.run(
      `UPDATE clients SET ${updates.join(', ')} WHERE id = ?`,
      values
    )

    const client = await this.getById(id)
    if (!client) throw new Error('Client not found')
    return client
  }

  static async delete(id: number): Promise<void> {
    await db.run('DELETE FROM clients WHERE id = ?', [id])
  }
}
