import { NextRequest, NextResponse } from 'next/server'
import { ClientService } from '@/lib/services/clientService'
import { getCloudronUser } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const clients = await ClientService.getAll()
    return NextResponse.json({ clients })
  } catch (error) {
    console.error('Erreur API clients GET:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, hourlyRate } = body

    if (!name) {
      return NextResponse.json({ 
        error: 'Le nom est requis' 
      }, { status: 400 })
    }

    const client = await ClientService.create({
      name,
      email,
      hourlyRate: hourlyRate ? parseFloat(hourlyRate) : undefined
    })

    return NextResponse.json({ client }, { status: 201 })
  } catch (error) {
    console.error('Erreur API clients POST:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}
