import { NextRequest, NextResponse } from 'next/server'
import { TimeEntryService } from '@/lib/services/timeEntryService'
import { getCloudronUser } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    let timeEntries
    if (startDate && endDate) {
      timeEntries = await TimeEntryService.getByDateRange(user.id, startDate, endDate)
    } else {
      timeEntries = await TimeEntryService.getAll(user.id)
    }

    return NextResponse.json({ timeEntries })
  } catch (error) {
    console.error('Erreur API time-entries GET:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, description, startTime, endTime, duration, isRunning = false } = body

    if (!projectId || !startTime) {
      return NextResponse.json({ 
        error: 'Le projet et l\'heure de début sont requis' 
      }, { status: 400 })
    }

    const timeEntry = await TimeEntryService.create({
      projectId: parseInt(projectId),
      description,
      startTime,
      endTime,
      duration: duration ? parseInt(duration) : undefined,
      isRunning,
      userId: user.id
    })

    return NextResponse.json({ timeEntry }, { status: 201 })
  } catch (error) {
    console.error('Erreur API time-entries POST:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}
