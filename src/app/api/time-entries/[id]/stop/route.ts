import { NextRequest, NextResponse } from 'next/server'
import { TimeEntryService } from '@/lib/services/timeEntryService'
import { getCloudronUser } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const resolvedParams = await params
    const timeEntryId = parseInt(resolvedParams.id)
    if (isNaN(timeEntryId)) {
      return NextResponse.json({ 
        error: 'ID d\'entrée invalide' 
      }, { status: 400 })
    }

    const timeEntry = await TimeEntryService.stopTimer(timeEntryId, user.id)

    return NextResponse.json({ timeEntry })
  } catch (error) {
    console.error('Erreur API time-entries stop:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Erreur serveur' 
    }, { status: 500 })
  }
}
