import { NextRequest, NextResponse } from 'next/server'
import { TimeEntryService } from '@/lib/services/timeEntryService'
import { getCloudronUser } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const body = await request.json()
    const { projectId, description } = body

    if (!projectId) {
      return NextResponse.json({ 
        error: 'Le projet est requis' 
      }, { status: 400 })
    }

    const timeEntry = await TimeEntryService.startTimer(
      parseInt(projectId),
      user.id,
      description
    )

    return NextResponse.json({ timeEntry }, { status: 201 })
  } catch (error) {
    console.error('Erreur API time-entries start:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}
