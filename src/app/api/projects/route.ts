import { NextRequest, NextResponse } from 'next/server'
import { ProjectService } from '@/lib/services/projectService'
import { getCloudronUser } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const projects = await ProjectService.getAll()
    return NextResponse.json({ projects })
  } catch (error) {
    console.error('Erreur API projects GET:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = getCloudronUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })
    }

    const body = await request.json()
    const { name, description, clientId, hourlyRate, isActive = true } = body

    if (!name || !clientId) {
      return NextResponse.json({ 
        error: 'Le nom et le client sont requis' 
      }, { status: 400 })
    }

    const project = await ProjectService.create({
      name,
      description,
      clientId: parseInt(clientId),
      hourlyRate: hourlyRate ? parseFloat(hourlyRate) : undefined,
      isActive
    })

    return NextResponse.json({ project }, { status: 201 })
  } catch (error) {
    console.error('Erreur API projects POST:', error)
    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })
  }
}
