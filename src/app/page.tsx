import { AppLayout } from '@/components/AppLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Play, Square, Clock } from 'lucide-react'

export default function Home() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Suivi du temps</h1>
          <p className="text-muted-foreground">
            Gérez votre temps de travail efficacement
          </p>
        </div>

        {/* Timer principal */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timer actuel
            </CardTitle>
            <CardDescription>
              Démarrez un nouveau timer ou continuez votre travail en cours
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="text-4xl font-mono font-bold">00:00:00</div>
              <p className="text-sm text-muted-foreground mt-2">
                Aucun timer en cours
              </p>
            </div>
            <div className="flex justify-center gap-2">
              <Button size="lg" className="gap-2">
                <Play className="h-4 w-4" />
                Démarrer
              </Button>
              <Button size="lg" variant="outline" className="gap-2" disabled>
                <Square className="h-4 w-4" />
                Arrêter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Entrées récentes */}
        <Card>
          <CardHeader>
            <CardTitle>Entrées récentes</CardTitle>
            <CardDescription>
              Vos dernières sessions de travail
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              Aucune entrée de temps pour le moment.
              <br />
              Commencez par démarrer un timer !
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
