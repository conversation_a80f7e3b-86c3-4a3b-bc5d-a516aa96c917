module.exports = [
"[project]/git/trackify/.next-internal/server/app/api/time-entries/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/sqlite3 [external] (sqlite3, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("sqlite3", () => require("sqlite3"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[project]/git/trackify/src/lib/database.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "db",
    ()=>db
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$sqlite3__$5b$external$5d$__$28$sqlite3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/sqlite3 [external] (sqlite3, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/util [external] (util, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
class Database {
    db = null;
    async connect() {
        if (this.db) return this.db;
        const dbPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data', 'trackify.db');
        // Créer le dossier data s'il n'existe pas
        const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
        const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, {
                recursive: true
            });
        }
        this.db = new __TURBOPACK__imported__module__$5b$externals$5d2f$sqlite3__$5b$external$5d$__$28$sqlite3$2c$__cjs$29$__["default"].Database(dbPath);
        // Promisifier les méthodes
        const run = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.run.bind(this.db));
        const get = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.get.bind(this.db));
        const all = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.all.bind(this.db));
        // Créer les tables
        await this.createTables();
        return this.db;
    }
    async createTables() {
        if (!this.db) throw new Error('Database not connected');
        const run = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.run.bind(this.db));
        // Table clients
        await run(`
      CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        hourlyRate REAL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
        // Table projets
        await run(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        clientId INTEGER NOT NULL,
        hourlyRate REAL,
        isActive BOOLEAN DEFAULT 1,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clientId) REFERENCES clients (id)
      )
    `);
        // Table entrées de temps
        await run(`
      CREATE TABLE IF NOT EXISTS timeEntries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        projectId INTEGER NOT NULL,
        description TEXT,
        startTime DATETIME NOT NULL,
        endTime DATETIME,
        duration INTEGER,
        isRunning BOOLEAN DEFAULT 0,
        userId TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (projectId) REFERENCES projects (id)
      )
    `);
        // Index pour les performances
        await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_userId ON timeEntries (userId)`);
        await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_projectId ON timeEntries (projectId)`);
        await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_startTime ON timeEntries (startTime)`);
    }
    async query(sql, params = []) {
        await this.connect();
        if (!this.db) throw new Error('Database not connected');
        const all = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.all.bind(this.db));
        return all(sql, params);
    }
    async get(sql, params = []) {
        await this.connect();
        if (!this.db) throw new Error('Database not connected');
        const get = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.get.bind(this.db));
        return get(sql, params);
    }
    async run(sql, params = []) {
        await this.connect();
        if (!this.db) throw new Error('Database not connected');
        return new Promise((resolve, reject)=>{
            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        lastID: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }
    async close() {
        if (this.db) {
            const close = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["promisify"])(this.db.close.bind(this.db));
            await close();
            this.db = null;
        }
    }
}
const db = new Database();
}),
"[project]/git/trackify/src/lib/services/timeEntryService.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TimeEntryService",
    ()=>TimeEntryService
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/src/lib/database.ts [app-route] (ecmascript)");
;
class TimeEntryService {
    static async getAll(userId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.userId = ? 
      ORDER BY t.startTime DESC
    `, [
            userId
        ]);
    }
    static async getById(id, userId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].get(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.id = ? AND t.userId = ?
    `, [
            id,
            userId
        ]);
    }
    static async getRunning(userId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].get(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.userId = ? AND t.isRunning = 1
    `, [
            userId
        ]);
    }
    static async getByDateRange(userId, startDate, endDate) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query(`
      SELECT t.*, p.name as projectName, c.name as clientName 
      FROM timeEntries t 
      LEFT JOIN projects p ON t.projectId = p.id 
      LEFT JOIN clients c ON p.clientId = c.id 
      WHERE t.userId = ? AND DATE(t.startTime) BETWEEN ? AND ? 
      ORDER BY t.startTime DESC
    `, [
            userId,
            startDate,
            endDate
        ]);
    }
    static async create(data) {
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].run('INSERT INTO timeEntries (projectId, description, startTime, endTime, duration, isRunning, userId) VALUES (?, ?, ?, ?, ?, ?, ?)', [
            data.projectId,
            data.description,
            data.startTime,
            data.endTime,
            data.duration,
            data.isRunning ? 1 : 0,
            data.userId
        ]);
        const timeEntry = await this.getById(result.lastID, data.userId);
        if (!timeEntry) throw new Error('Failed to create time entry');
        return timeEntry;
    }
    static async startTimer(projectId, userId, description) {
        // Arrêter tout timer en cours
        await this.stopAllRunningTimers(userId);
        const startTime = new Date().toISOString();
        return this.create({
            projectId,
            description,
            startTime,
            isRunning: true,
            userId
        });
    }
    static async stopTimer(id, userId) {
        const timeEntry = await this.getById(id, userId);
        if (!timeEntry) throw new Error('Time entry not found');
        if (!timeEntry.isRunning) throw new Error('Timer is not running');
        const endTime = new Date().toISOString();
        const startTime = new Date(timeEntry.startTime);
        const duration = Math.floor((new Date(endTime).getTime() - startTime.getTime()) / 1000 / 60) // en minutes
        ;
        await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].run('UPDATE timeEntries SET endTime = ?, duration = ?, isRunning = 0, updatedAt = CURRENT_TIMESTAMP WHERE id = ? AND userId = ?', [
            endTime,
            duration,
            id,
            userId
        ]);
        const updatedEntry = await this.getById(id, userId);
        if (!updatedEntry) throw new Error('Failed to update time entry');
        return updatedEntry;
    }
    static async stopAllRunningTimers(userId) {
        const runningEntries = await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].query('SELECT * FROM timeEntries WHERE userId = ? AND isRunning = 1', [
            userId
        ]);
        for (const entry of runningEntries){
            await this.stopTimer(entry.id, userId);
        }
    }
    static async update(id, userId, data) {
        const updates = [];
        const values = [];
        if (data.projectId !== undefined) {
            updates.push('projectId = ?');
            values.push(data.projectId);
        }
        if (data.description !== undefined) {
            updates.push('description = ?');
            values.push(data.description);
        }
        if (data.startTime !== undefined) {
            updates.push('startTime = ?');
            values.push(data.startTime);
        }
        if (data.endTime !== undefined) {
            updates.push('endTime = ?');
            values.push(data.endTime);
        }
        if (data.duration !== undefined) {
            updates.push('duration = ?');
            values.push(data.duration);
        }
        if (updates.length === 0) {
            const timeEntry = await this.getById(id, userId);
            if (!timeEntry) throw new Error('Time entry not found');
            return timeEntry;
        }
        updates.push('updatedAt = CURRENT_TIMESTAMP');
        values.push(id, userId);
        await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].run(`UPDATE timeEntries SET ${updates.join(', ')} WHERE id = ? AND userId = ?`, values);
        const timeEntry = await this.getById(id, userId);
        if (!timeEntry) throw new Error('Time entry not found');
        return timeEntry;
    }
    static async delete(id, userId) {
        await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].run('DELETE FROM timeEntries WHERE id = ? AND userId = ?', [
            id,
            userId
        ]);
    }
}
}),
"[project]/git/trackify/src/lib/auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "createMockCloudronHeaders",
    ()=>createMockCloudronHeaders,
    "getCloudronUser",
    ()=>getCloudronUser,
    "isAuthenticated",
    ()=>isAuthenticated,
    "requireAuth",
    ()=>requireAuth
]);
function getCloudronUser(request) {
    try {
        // Headers Cloudron standards
        const userId = request.headers.get('x-cloudron-user-id');
        const username = request.headers.get('x-cloudron-username');
        const email = request.headers.get('x-cloudron-user-email');
        const displayName = request.headers.get('x-cloudron-user-displayname');
        const groups = request.headers.get('x-cloudron-user-groups');
        if (!userId || !username || !email) {
            return null;
        }
        return {
            id: userId,
            username,
            email,
            displayName: displayName || username,
            groups: groups ? groups.split(',').map((g)=>g.trim()) : []
        };
    } catch (error) {
        console.error('Erreur lors de l\'extraction des informations utilisateur Cloudron:', error);
        return null;
    }
}
function isAuthenticated(request) {
    const user = getCloudronUser(request);
    return user !== null;
}
function requireAuth(request) {
    const user = getCloudronUser(request);
    if (!user) {
        throw new Error('Utilisateur non authentifié');
    }
    return user;
}
function createMockCloudronHeaders() {
    if ("TURBOPACK compile-time truthy", 1) {
        return {
            'x-cloudron-user-id': 'dev-user-1',
            'x-cloudron-username': 'developer',
            'x-cloudron-user-email': '<EMAIL>',
            'x-cloudron-user-displayname': 'Developer User',
            'x-cloudron-user-groups': 'admin,users'
        };
    }
    //TURBOPACK unreachable
    ;
}
}),
"[project]/git/trackify/src/app/api/time-entries/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$services$2f$timeEntryService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/src/lib/services/timeEntryService.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/src/lib/auth.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCloudronUser"])(request);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Non authentifié'
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        let timeEntries;
        if (startDate && endDate) {
            timeEntries = await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$services$2f$timeEntryService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TimeEntryService"].getByDateRange(user.id, startDate, endDate);
        } else {
            timeEntries = await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$services$2f$timeEntryService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TimeEntryService"].getAll(user.id);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            timeEntries
        });
    } catch (error) {
        console.error('Erreur API time-entries GET:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur serveur'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCloudronUser"])(request);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Non authentifié'
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const { projectId, description, startTime, endTime, duration, isRunning = false } = body;
        if (!projectId || !startTime) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Le projet et l\'heure de début sont requis'
            }, {
                status: 400
            });
        }
        const timeEntry = await __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$services$2f$timeEntryService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["TimeEntryService"].create({
            projectId: parseInt(projectId),
            description,
            startTime,
            endTime,
            duration: duration ? parseInt(duration) : undefined,
            isRunning,
            userId: user.id
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            timeEntry
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Erreur API time-entries POST:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur serveur'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__fd33f365._.js.map