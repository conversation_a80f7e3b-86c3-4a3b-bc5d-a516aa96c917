module.exports = [
"[project]/git/trackify/.next-internal/server/app/api/auth/user/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/git/trackify/src/lib/auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "createMockCloudronHeaders",
    ()=>createMockCloudronHeaders,
    "getCloudronUser",
    ()=>getCloudronUser,
    "isAuthenticated",
    ()=>isAuthenticated,
    "requireAuth",
    ()=>requireAuth
]);
function getCloudronUser(request) {
    try {
        // Headers Cloudron standards
        const userId = request.headers.get('x-cloudron-user-id');
        const username = request.headers.get('x-cloudron-username');
        const email = request.headers.get('x-cloudron-user-email');
        const displayName = request.headers.get('x-cloudron-user-displayname');
        const groups = request.headers.get('x-cloudron-user-groups');
        if (!userId || !username || !email) {
            return null;
        }
        return {
            id: userId,
            username,
            email,
            displayName: displayName || username,
            groups: groups ? groups.split(',').map((g)=>g.trim()) : []
        };
    } catch (error) {
        console.error('Erreur lors de l\'extraction des informations utilisateur Cloudron:', error);
        return null;
    }
}
function isAuthenticated(request) {
    const user = getCloudronUser(request);
    return user !== null;
}
function requireAuth(request) {
    const user = getCloudronUser(request);
    if (!user) {
        throw new Error('Utilisateur non authentifié');
    }
    return user;
}
function createMockCloudronHeaders() {
    if ("TURBOPACK compile-time truthy", 1) {
        return {
            'x-cloudron-user-id': 'dev-user-1',
            'x-cloudron-username': 'developer',
            'x-cloudron-user-email': '<EMAIL>',
            'x-cloudron-user-displayname': 'Developer User',
            'x-cloudron-user-groups': 'admin,users'
        };
    }
    //TURBOPACK unreachable
    ;
}
}),
"[project]/git/trackify/src/app/api/auth/user/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/src/lib/auth.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getCloudronUser"])(request);
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Non authentifié'
            }, {
                status: 401
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            user
        });
    } catch (error) {
        console.error('Erreur API auth/user:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Erreur serveur'
        }, {
            status: 500
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__7e93f389._.js.map