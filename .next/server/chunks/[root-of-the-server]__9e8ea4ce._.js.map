{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/database.ts"], "sourcesContent": ["import sqlite3 from 'sqlite3'\nimport { promisify } from 'util'\nimport path from 'path'\n\n// Types pour notre base de données\nexport interface Client {\n  id: number\n  name: string\n  email?: string\n  hourlyRate?: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Project {\n  id: number\n  name: string\n  description?: string\n  clientId: number\n  hourlyRate?: number\n  isActive: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TimeEntry {\n  id: number\n  projectId: number\n  description?: string\n  startTime: string\n  endTime?: string\n  duration?: number // en minutes\n  isRunning: boolean\n  userId: string\n  createdAt: string\n  updatedAt: string\n}\n\nclass Database {\n  private db: sqlite3.Database | null = null\n\n  async connect() {\n    if (this.db) return this.db\n\n    const dbPath = path.join(process.cwd(), 'data', 'trackify.db')\n    \n    // Créer le dossier data s'il n'existe pas\n    const fs = require('fs')\n    const dataDir = path.dirname(dbPath)\n    if (!fs.existsSync(dataDir)) {\n      fs.mkdirSync(dataDir, { recursive: true })\n    }\n\n    this.db = new sqlite3.Database(dbPath)\n    \n    // Promisifier les méthodes\n    const run = promisify(this.db.run.bind(this.db))\n    const get = promisify(this.db.get.bind(this.db))\n    const all = promisify(this.db.all.bind(this.db))\n\n    // Créer les tables\n    await this.createTables()\n    \n    return this.db\n  }\n\n  private async createTables() {\n    if (!this.db) throw new Error('Database not connected')\n\n    const run = promisify(this.db.run.bind(this.db))\n\n    // Table clients\n    await run(`\n      CREATE TABLE IF NOT EXISTS clients (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL,\n        email TEXT,\n        hourlyRate REAL,\n        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `)\n\n    // Table projets\n    await run(`\n      CREATE TABLE IF NOT EXISTS projects (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL,\n        description TEXT,\n        clientId INTEGER NOT NULL,\n        hourlyRate REAL,\n        isActive BOOLEAN DEFAULT 1,\n        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (clientId) REFERENCES clients (id)\n      )\n    `)\n\n    // Table entrées de temps\n    await run(`\n      CREATE TABLE IF NOT EXISTS timeEntries (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        projectId INTEGER NOT NULL,\n        description TEXT,\n        startTime DATETIME NOT NULL,\n        endTime DATETIME,\n        duration INTEGER,\n        isRunning BOOLEAN DEFAULT 0,\n        userId TEXT NOT NULL,\n        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (projectId) REFERENCES projects (id)\n      )\n    `)\n\n    // Index pour les performances\n    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_userId ON timeEntries (userId)`)\n    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_projectId ON timeEntries (projectId)`)\n    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_startTime ON timeEntries (startTime)`)\n  }\n\n  async query(sql: string, params: any[] = []): Promise<any[]> {\n    await this.connect()\n    if (!this.db) throw new Error('Database not connected')\n\n    const all = promisify(this.db.all.bind(this.db))\n    return all(sql, params)\n  }\n\n  async get(sql: string, params: any[] = []): Promise<any> {\n    await this.connect()\n    if (!this.db) throw new Error('Database not connected')\n\n    const get = promisify(this.db.get.bind(this.db))\n    return get(sql, params)\n  }\n\n  async run(sql: string, params: any[] = []): Promise<{ lastID: number; changes: number }> {\n    await this.connect()\n    if (!this.db) throw new Error('Database not connected')\n\n    const run = promisify(this.db.run.bind(this.db))\n    return run(sql, params)\n  }\n\n  async close() {\n    if (this.db) {\n      const close = promisify(this.db.close.bind(this.db))\n      await close()\n      this.db = null\n    }\n  }\n}\n\n// Instance singleton\nexport const db = new Database()\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAoCA,MAAM;IACI,KAA8B,KAAI;IAE1C,MAAM,UAAU;QACd,IAAI,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE;QAE3B,MAAM,SAAS,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QAEhD,0CAA0C;QAC1C,MAAM;QACN,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,GAAG,UAAU,CAAC,UAAU;YAC3B,GAAG,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;QAEA,IAAI,CAAC,EAAE,GAAG,IAAI,kHAAO,CAAC,QAAQ,CAAC;QAE/B,2BAA2B;QAC3B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAE9C,mBAAmB;QACnB,MAAM,IAAI,CAAC,YAAY;QAEvB,OAAO,IAAI,CAAC,EAAE;IAChB;IAEA,MAAc,eAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAE9C,gBAAgB;QAChB,MAAM,IAAI,CAAC;;;;;;;;;IASX,CAAC;QAED,gBAAgB;QAChB,MAAM,IAAI,CAAC;;;;;;;;;;;;IAYX,CAAC;QAED,yBAAyB;QACzB,MAAM,IAAI,CAAC;;;;;;;;;;;;;;IAcX,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,CAAC,yEAAyE,CAAC;QACrF,MAAM,IAAI,CAAC,+EAA+E,CAAC;QAC3F,MAAM,IAAI,CAAC,+EAA+E,CAAC;IAC7F;IAEA,MAAM,MAAM,GAAW,EAAE,SAAgB,EAAE,EAAkB;QAC3D,MAAM,IAAI,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,OAAO,IAAI,KAAK;IAClB;IAEA,MAAM,IAAI,GAAW,EAAE,SAAgB,EAAE,EAAgB;QACvD,MAAM,IAAI,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,OAAO,IAAI,KAAK;IAClB;IAEA,MAAM,IAAI,GAAW,EAAE,SAAgB,EAAE,EAAgD;QACvF,MAAM,IAAI,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,OAAO,IAAI,KAAK;IAClB;IAEA,MAAM,QAAQ;QACZ,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,MAAM,QAAQ,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAClD,MAAM;YACN,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF;AAGO,MAAM,KAAK,IAAI", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/services/timeEntryService.ts"], "sourcesContent": ["import { db, TimeEntry } from '../database'\n\nexport class TimeEntryService {\n  static async getAll(userId: string): Promise<TimeEntry[]> {\n    return db.query(`\n      SELECT t.*, p.name as projectName, c.name as clientName \n      FROM timeEntries t \n      LEFT JOIN projects p ON t.projectId = p.id \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE t.userId = ? \n      ORDER BY t.startTime DESC\n    `, [userId])\n  }\n\n  static async getById(id: number, userId: string): Promise<TimeEntry | null> {\n    return db.get(`\n      SELECT t.*, p.name as projectName, c.name as clientName \n      FROM timeEntries t \n      LEFT JOIN projects p ON t.projectId = p.id \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE t.id = ? AND t.userId = ?\n    `, [id, userId])\n  }\n\n  static async getRunning(userId: string): Promise<TimeEntry | null> {\n    return db.get(`\n      SELECT t.*, p.name as projectName, c.name as clientName \n      FROM timeEntries t \n      LEFT JOIN projects p ON t.projectId = p.id \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE t.userId = ? AND t.isRunning = 1\n    `, [userId])\n  }\n\n  static async getByDateRange(userId: string, startDate: string, endDate: string): Promise<TimeEntry[]> {\n    return db.query(`\n      SELECT t.*, p.name as projectName, c.name as clientName \n      FROM timeEntries t \n      LEFT JOIN projects p ON t.projectId = p.id \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE t.userId = ? AND DATE(t.startTime) BETWEEN ? AND ? \n      ORDER BY t.startTime DESC\n    `, [userId, startDate, endDate])\n  }\n\n  static async create(data: Omit<TimeEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<TimeEntry> {\n    const result = await db.run(\n      'INSERT INTO timeEntries (projectId, description, startTime, endTime, duration, isRunning, userId) VALUES (?, ?, ?, ?, ?, ?, ?)',\n      [data.projectId, data.description, data.startTime, data.endTime, data.duration, data.isRunning ? 1 : 0, data.userId]\n    )\n    \n    const timeEntry = await this.getById(result.lastID, data.userId)\n    if (!timeEntry) throw new Error('Failed to create time entry')\n    return timeEntry\n  }\n\n  static async startTimer(projectId: number, userId: string, description?: string): Promise<TimeEntry> {\n    // Arrêter tout timer en cours\n    await this.stopAllRunningTimers(userId)\n\n    const startTime = new Date().toISOString()\n    return this.create({\n      projectId,\n      description,\n      startTime,\n      isRunning: true,\n      userId\n    })\n  }\n\n  static async stopTimer(id: number, userId: string): Promise<TimeEntry> {\n    const timeEntry = await this.getById(id, userId)\n    if (!timeEntry) throw new Error('Time entry not found')\n    if (!timeEntry.isRunning) throw new Error('Timer is not running')\n\n    const endTime = new Date().toISOString()\n    const startTime = new Date(timeEntry.startTime)\n    const duration = Math.floor((new Date(endTime).getTime() - startTime.getTime()) / 1000 / 60) // en minutes\n\n    await db.run(\n      'UPDATE timeEntries SET endTime = ?, duration = ?, isRunning = 0, updatedAt = CURRENT_TIMESTAMP WHERE id = ? AND userId = ?',\n      [endTime, duration, id, userId]\n    )\n\n    const updatedEntry = await this.getById(id, userId)\n    if (!updatedEntry) throw new Error('Failed to update time entry')\n    return updatedEntry\n  }\n\n  static async stopAllRunningTimers(userId: string): Promise<void> {\n    const runningEntries = await db.query(\n      'SELECT * FROM timeEntries WHERE userId = ? AND isRunning = 1',\n      [userId]\n    )\n\n    for (const entry of runningEntries) {\n      await this.stopTimer(entry.id, userId)\n    }\n  }\n\n  static async update(id: number, userId: string, data: Partial<Omit<TimeEntry, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>): Promise<TimeEntry> {\n    const updates: string[] = []\n    const values: any[] = []\n\n    if (data.projectId !== undefined) {\n      updates.push('projectId = ?')\n      values.push(data.projectId)\n    }\n    if (data.description !== undefined) {\n      updates.push('description = ?')\n      values.push(data.description)\n    }\n    if (data.startTime !== undefined) {\n      updates.push('startTime = ?')\n      values.push(data.startTime)\n    }\n    if (data.endTime !== undefined) {\n      updates.push('endTime = ?')\n      values.push(data.endTime)\n    }\n    if (data.duration !== undefined) {\n      updates.push('duration = ?')\n      values.push(data.duration)\n    }\n\n    if (updates.length === 0) {\n      const timeEntry = await this.getById(id, userId)\n      if (!timeEntry) throw new Error('Time entry not found')\n      return timeEntry\n    }\n\n    updates.push('updatedAt = CURRENT_TIMESTAMP')\n    values.push(id, userId)\n\n    await db.run(\n      `UPDATE timeEntries SET ${updates.join(', ')} WHERE id = ? AND userId = ?`,\n      values\n    )\n\n    const timeEntry = await this.getById(id, userId)\n    if (!timeEntry) throw new Error('Time entry not found')\n    return timeEntry\n  }\n\n  static async delete(id: number, userId: string): Promise<void> {\n    await db.run('DELETE FROM timeEntries WHERE id = ? AND userId = ?', [id, userId])\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM;IACX,aAAa,OAAO,MAAc,EAAwB;QACxD,OAAO,iJAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;IAOjB,CAAC,EAAE;YAAC;SAAO;IACb;IAEA,aAAa,QAAQ,EAAU,EAAE,MAAc,EAA6B;QAC1E,OAAO,iJAAE,CAAC,GAAG,CAAC,CAAC;;;;;;IAMf,CAAC,EAAE;YAAC;YAAI;SAAO;IACjB;IAEA,aAAa,WAAW,MAAc,EAA6B;QACjE,OAAO,iJAAE,CAAC,GAAG,CAAC,CAAC;;;;;;IAMf,CAAC,EAAE;YAAC;SAAO;IACb;IAEA,aAAa,eAAe,MAAc,EAAE,SAAiB,EAAE,OAAe,EAAwB;QACpG,OAAO,iJAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;IAOjB,CAAC,EAAE;YAAC;YAAQ;YAAW;SAAQ;IACjC;IAEA,aAAa,OAAO,IAAuD,EAAsB;QAC/F,MAAM,SAAS,MAAM,iJAAE,CAAC,GAAG,CACzB,kIACA;YAAC,KAAK,SAAS;YAAE,KAAK,WAAW;YAAE,KAAK,SAAS;YAAE,KAAK,OAAO;YAAE,KAAK,QAAQ;YAAE,KAAK,SAAS,GAAG,IAAI;YAAG,KAAK,MAAM;SAAC;QAGtH,MAAM,YAAY,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM,EAAE,KAAK,MAAM;QAC/D,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;QAChC,OAAO;IACT;IAEA,aAAa,WAAW,SAAiB,EAAE,MAAc,EAAE,WAAoB,EAAsB;QACnG,8BAA8B;QAC9B,MAAM,IAAI,CAAC,oBAAoB,CAAC;QAEhC,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC;YACjB;YACA;YACA;YACA,WAAW;YACX;QACF;IACF;IAEA,aAAa,UAAU,EAAU,EAAE,MAAc,EAAsB;QACrE,MAAM,YAAY,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;QACzC,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;QAChC,IAAI,CAAC,UAAU,SAAS,EAAE,MAAM,IAAI,MAAM;QAE1C,MAAM,UAAU,IAAI,OAAO,WAAW;QACtC,MAAM,YAAY,IAAI,KAAK,UAAU,SAAS;QAC9C,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,SAAS,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,OAAO,IAAI,aAAa;;QAE1G,MAAM,iJAAE,CAAC,GAAG,CACV,8HACA;YAAC;YAAS;YAAU;YAAI;SAAO;QAGjC,MAAM,eAAe,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;QAC5C,IAAI,CAAC,cAAc,MAAM,IAAI,MAAM;QACnC,OAAO;IACT;IAEA,aAAa,qBAAqB,MAAc,EAAiB;QAC/D,MAAM,iBAAiB,MAAM,iJAAE,CAAC,KAAK,CACnC,gEACA;YAAC;SAAO;QAGV,KAAK,MAAM,SAAS,eAAgB;YAClC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE;QACjC;IACF;IAEA,aAAa,OAAO,EAAU,EAAE,MAAc,EAAE,IAA2E,EAAsB;QAC/I,MAAM,UAAoB,EAAE;QAC5B,MAAM,SAAgB,EAAE;QAExB,IAAI,KAAK,SAAS,KAAK,WAAW;YAChC,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,SAAS;QAC5B;QACA,IAAI,KAAK,WAAW,KAAK,WAAW;YAClC,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,WAAW;QAC9B;QACA,IAAI,KAAK,SAAS,KAAK,WAAW;YAChC,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,SAAS;QAC5B;QACA,IAAI,KAAK,OAAO,KAAK,WAAW;YAC9B,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,OAAO;QAC1B;QACA,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC/B,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,QAAQ;QAC3B;QAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,MAAM,YAAY,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;YACzC,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;YAChC,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO,IAAI,CAAC,IAAI;QAEhB,MAAM,iJAAE,CAAC,GAAG,CACV,CAAC,uBAAuB,EAAE,QAAQ,IAAI,CAAC,MAAM,4BAA4B,CAAC,EAC1E;QAGF,MAAM,YAAY,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;QACzC,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;QAChC,OAAO;IACT;IAEA,aAAa,OAAO,EAAU,EAAE,MAAc,EAAiB;QAC7D,MAAM,iJAAE,CAAC,GAAG,CAAC,uDAAuD;YAAC;YAAI;SAAO;IAClF;AACF", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface CloudronUser {\n  id: string\n  username: string\n  email: string\n  displayName: string\n  groups: string[]\n}\n\n/**\n * Extrait les informations utilisateur des headers Cloudron\n */\nexport function getCloudronUser(request: NextRequest): CloudronUser | null {\n  try {\n    // Headers Cloudron standards\n    const userId = request.headers.get('x-cloudron-user-id')\n    const username = request.headers.get('x-cloudron-username')\n    const email = request.headers.get('x-cloudron-user-email')\n    const displayName = request.headers.get('x-cloudron-user-displayname')\n    const groups = request.headers.get('x-cloudron-user-groups')\n\n    if (!userId || !username || !email) {\n      return null\n    }\n\n    return {\n      id: userId,\n      username,\n      email,\n      displayName: displayName || username,\n      groups: groups ? groups.split(',').map(g => g.trim()) : []\n    }\n  } catch (error) {\n    console.error('Erreur lors de l\\'extraction des informations utilisateur Cloudron:', error)\n    return null\n  }\n}\n\n/**\n * Vérifie si l'utilisateur est authentifié via Cloudron\n */\nexport function isAuthenticated(request: NextRequest): boolean {\n  const user = getCloudronUser(request)\n  return user !== null\n}\n\n/**\n * Middleware pour vérifier l'authentification Cloudron\n */\nexport function requireAuth(request: NextRequest): CloudronUser {\n  const user = getCloudronUser(request)\n  \n  if (!user) {\n    throw new Error('Utilisateur non authentifié')\n  }\n  \n  return user\n}\n\n/**\n * Utilitaire pour le développement local (simulation des headers Cloudron)\n */\nexport function createMockCloudronHeaders(): Record<string, string> {\n  if (process.env.NODE_ENV === 'development') {\n    return {\n      'x-cloudron-user-id': 'dev-user-1',\n      'x-cloudron-username': 'developer',\n      'x-cloudron-user-email': '<EMAIL>',\n      'x-cloudron-user-displayname': 'Developer User',\n      'x-cloudron-user-groups': 'admin,users'\n    }\n  }\n  return {}\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAaO,SAAS,gBAAgB,OAAoB;IAClD,IAAI;QACF,6BAA6B;QAC7B,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;QACrC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;QAClC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO;YAClC,OAAO;QACT;QAEA,OAAO;YACL,IAAI;YACJ;YACA;YACA,aAAa,eAAe;YAC5B,QAAQ,SAAS,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM,EAAE;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uEAAuE;QACrF,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,OAAO,gBAAgB;IAC7B,OAAO,SAAS;AAClB;AAKO,SAAS,YAAY,OAAoB;IAC9C,MAAM,OAAO,gBAAgB;IAE7B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAKO,SAAS;IACd,wCAA4C;QAC1C,OAAO;YACL,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,+BAA+B;YAC/B,0BAA0B;QAC5B;IACF;;;AAEF", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/app/api/time-entries/start/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { TimeEntryService } from '@/lib/services/timeEntryService'\nimport { getCloudronUser } from '@/lib/auth'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const user = getCloudronUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const { projectId, description } = body\n\n    if (!projectId) {\n      return NextResponse.json({ \n        error: 'Le projet est requis' \n      }, { status: 400 })\n    }\n\n    const timeEntry = await TimeEntryService.startTimer(\n      parseInt(projectId),\n      user.id,\n      description\n    )\n\n    return NextResponse.json({ timeEntry }, { status: 201 })\n  } catch (error) {\n    console.error('Erreur API time-entries start:', error)\n    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,IAAA,0JAAe,EAAC;QAC7B,IAAI,CAAC,MAAM;YACT,OAAO,mKAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;QAEnC,IAAI,CAAC,WAAW;YACd,OAAO,mKAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,YAAY,MAAM,mLAAgB,CAAC,UAAU,CACjD,SAAS,YACT,KAAK,EAAE,EACP;QAGF,OAAO,mKAAY,CAAC,IAAI,CAAC;YAAE;QAAU,GAAG;YAAE,QAAQ;QAAI;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,mKAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}