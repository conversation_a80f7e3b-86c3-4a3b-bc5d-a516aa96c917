{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,yLAAO,EAAC,IAAA,gKAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,0LAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,2LAAI,GAAG;IAE9B,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,iQAAC,6LAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,iQAAC,8LAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,iQAAC,iMAAwB;QACvB,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,iQAAC,uMAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,iQAAC,yMAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,iQAAC,0MAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,iQAAC,yMAA4B;kBAC3B,cAAA,iQAAC,0MAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,4IAAE,EACX,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,iQAAC,wMAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,iQAAC,uMAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,4IAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,iQAAC,+MAAkC;QACjC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,iQAAC;gBAAK,WAAU;0BACd,cAAA,iQAAC,gNAAmC;8BAClC,cAAA,iQAAC,wOAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,iQAAC,6MAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,iQAAC,4MAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,iQAAC;gBAAK,WAAU;0BACd,cAAA,iQAAC,gNAAmC;8BAClC,cAAA,iQAAC,2OAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,iQAAC,wMAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,4IAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,iQAAC,4MAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,iQAAC,sMAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,iQAAC,6MAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,4IAAE,EACX,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,iQAAC,iQAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,iQAAC,6MAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { CloudronUser } from '@/lib/auth'\n\nexport function useAuth() {\n  const [user, setUser] = useState<CloudronUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Récupérer les informations utilisateur depuis l'API\n    fetch('/api/auth/user')\n      .then(res => res.json())\n      .then(data => {\n        setUser(data.user || null)\n        setLoading(false)\n      })\n      .catch(error => {\n        console.error('Erreur lors de la récupération de l\\'utilisateur:', error)\n        setUser(null)\n        setLoading(false)\n      })\n  }, [])\n\n  return {\n    user,\n    loading,\n    isAuthenticated: !!user\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAKO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,oOAAQ,EAAsB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,oOAAQ,EAAC;IAEvC,IAAA,qOAAS,EAAC;QACR,sDAAsD;QACtD,MAAM,kBACH,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA;YACJ,QAAQ,KAAK,IAAI,IAAI;YACrB,WAAW;QACb,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,qDAAqD;YACnE,QAAQ;YACR,WAAW;QACb;IACJ,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;AACF", "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { \n  DropdownMenu, \n  DropdownMenuContent, \n  DropdownMenuItem, \n  DropdownMenuTrigger \n} from '@/components/ui/dropdown-menu'\nimport { useAuth } from '@/hooks/useAuth'\nimport { \n  Clock, \n  BarChart3, \n  FolderOpen, \n  Users, \n  User,\n  LogOut \n} from 'lucide-react'\n\nconst navigationItems = [\n  {\n    name: 'Temps',\n    href: '/',\n    icon: Clock,\n  },\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: BarChart3,\n  },\n  {\n    name: 'Projets',\n    href: '/projects',\n    icon: FolderOpen,\n  },\n  {\n    name: 'Clients',\n    href: '/clients',\n    icon: Users,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container flex h-14 items-center\">\n          <div className=\"mr-4 hidden md:flex\">\n            <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n              <Clock className=\"h-6 w-6\" />\n              <span className=\"hidden font-bold sm:inline-block\">Trackify</span>\n            </Link>\n          </div>\n          <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n            <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n              <div className=\"h-8 w-8 rounded-full bg-muted animate-pulse\" />\n            </div>\n          </div>\n        </div>\n      </nav>\n    )\n  }\n\n  return (\n    <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <div className=\"mr-4 hidden md:flex\">\n          <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n            <Clock className=\"h-6 w-6\" />\n            <span className=\"hidden font-bold sm:inline-block\">Trackify</span>\n          </Link>\n          <nav className=\"flex items-center space-x-6 text-sm font-medium\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    \"flex items-center space-x-2 transition-colors hover:text-foreground/80\",\n                    pathname === item.href ? \"text-foreground\" : \"text-foreground/60\"\n                  )}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              )\n            })}\n          </nav>\n        </div>\n        <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n          <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n            {user && (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarFallback>\n                        {user.displayName.charAt(0).toUpperCase()}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuItem className=\"flex flex-col items-start\">\n                    <div className=\"font-medium\">{user.displayName}</div>\n                    <div className=\"text-xs text-muted-foreground\">{user.email}</div>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <User className=\"mr-2 h-4 w-4\" />\n                    <span>Profil</span>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    <span>Déconnexion</span>\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            )}\n          </div>\n        </div>\n      </div>\n      \n      {/* Navigation mobile */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-around border-t bg-background p-2\">\n          {navigationItems.map((item) => {\n            const Icon = item.icon\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  \"flex flex-col items-center space-y-1 rounded-md p-2 text-xs transition-colors hover:text-foreground/80\",\n                  pathname === item.href ? \"text-foreground bg-muted\" : \"text-foreground/60\"\n                )}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{item.name}</span>\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;AAuBA,MAAM,kBAAkB;IACtB;QACE,MAAM;QACN,MAAM;QACN,MAAM,gOAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kPAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mPAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gOAAK;IACb;CACD;AAEM,SAAS;IACd,MAAM,WAAW,IAAA,oKAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,qJAAO;IAEjC,IAAI,SAAS;QACX,qBACE,iQAAC;YAAI,WAAU;sBACb,cAAA,iQAAC;gBAAI,WAAU;;kCACb,iQAAC;wBAAI,WAAU;kCACb,cAAA,iQAAC,0LAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,iQAAC,gOAAK;oCAAC,WAAU;;;;;;8CACjB,iQAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAGvD,iQAAC;wBAAI,WAAU;kCACb,cAAA,iQAAC;4BAAI,WAAU;sCACb,cAAA,iQAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,iQAAC;QAAI,WAAU;;0BACb,iQAAC;gBAAI,WAAU;;kCACb,iQAAC;wBAAI,WAAU;;0CACb,iQAAC,0LAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,iQAAC,gOAAK;wCAAC,WAAU;;;;;;kDACjB,iQAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,iQAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC;oCACpB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,iQAAC,0LAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,IAAA,4IAAE,EACX,0EACA,aAAa,KAAK,IAAI,GAAG,oBAAoB;;0DAG/C,iQAAC;gDAAK,WAAU;;;;;;0DAChB,iQAAC;0DAAM,KAAK,IAAI;;;;;;;uCARX,KAAK,IAAI;;;;;gCAWpB;;;;;;;;;;;;kCAGJ,iQAAC;wBAAI,WAAU;kCACb,cAAA,iQAAC;4BAAI,WAAU;sCACZ,sBACC,iQAAC,+KAAY;;kDACX,iQAAC,sLAAmB;wCAAC,OAAO;kDAC1B,cAAA,iQAAC,+JAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,iQAAC,+JAAM;gDAAC,WAAU;0DAChB,cAAA,iQAAC,uKAAc;8DACZ,KAAK,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;kDAK/C,iQAAC,sLAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,iQAAC,mLAAgB;gDAAC,WAAU;;kEAC1B,iQAAC;wDAAI,WAAU;kEAAe,KAAK,WAAW;;;;;;kEAC9C,iQAAC;wDAAI,WAAU;kEAAiC,KAAK,KAAK;;;;;;;;;;;;0DAE5D,iQAAC,mLAAgB;;kEACf,iQAAC,6NAAI;wDAAC,WAAU;;;;;;kEAChB,iQAAC;kEAAK;;;;;;;;;;;;0DAER,iQAAC,mLAAgB;;kEACf,iQAAC,uOAAM;wDAAC,WAAU;;;;;;kEAClB,iQAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,iQAAC;gBAAI,WAAU;0BACb,cAAA,iQAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,iQAAC,0LAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,IAAA,4IAAE,EACX,0GACA,aAAa,KAAK,IAAI,GAAG,6BAA6B;;8CAGxD,iQAAC;oCAAK,WAAU;;;;;;8CAChB,iQAAC;8CAAM,KAAK,IAAI;;;;;;;2BARX,KAAK,IAAI;;;;;oBAWpB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './Navigation'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport function AppLayout({ children }: AppLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Navigation />\n      <main className=\"container mx-auto py-6\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAQO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,qBACE,iQAAC;QAAI,WAAU;;0BACb,iQAAC,iKAAU;;;;;0BACX,iQAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}]}