{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,YAAY,CAAEC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,WAAW,CAACC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackClient"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,YAAY,CAAEC,6BAA6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,YAAY,CAAEC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(\n    public readonly route: string,\n    public readonly expression: string\n  ) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  route: string,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(route, expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(route, expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n\nexport function makeDevtoolsIOAwarePromise<T>(underlying: T): Promise<T> {\n  // in React DevTools if we resolve in a setTimeout we will observe\n  // the promise resolution as something that can suspend a boundary or root.\n  return new Promise<T>((resolve) => {\n    // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n    setTimeout(() => {\n      resolve(underlying)\n    }, 0)\n  })\n}\n"], "names": ["isHangingPromiseRejectionError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "route", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "underlying", "resolve", "setTimeout"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IA2EAC,0BAA0B,EAAA;eAA1BA;;IAxCAC,kBAAkB,EAAA;eAAlBA;;;AAnCT,SAASF,+BACdG,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YACkBC,KAAa,EACbC,UAAkB,CAClC;QACA,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,8KAA8K,EAAED,MAAM,EAAE,CAAC,GAAA,IAAA,CAJhUA,KAAAA,GAAAA,OAAAA,IAAAA,CACAC,UAAAA,GAAAA,YAAAA,IAAAA,CAJFN,MAAAA,GAASC;IASzB;AACF;AAGA,MAAMM,yBAAyB,IAAIC;AAS5B,SAASV,mBACdW,MAAmB,EACnBJ,KAAa,EACbC,UAAkB;IAElB,IAAIG,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIV,6BAA6BG,OAAOC;IAChE,OAAO;QACL,MAAMO,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAId,6BAA6BG,OAAOC;YAE1C,IAAIW,mBAAmBV,uBAAuBW,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCR,uBAAuBc,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB;AAElB,SAAS9B,2BAA8B+B,UAAa;IACzD,kEAAkE;IAClE,2EAA2E;IAC3E,OAAO,IAAIjB,QAAW,CAACkB;QACrB,sFAAsF;QACtFC,WAAW;YACTD,QAAQD;QACV,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n  PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  getRuntimeStagePromise,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from '../../lib/framework/boundary-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n        // Inside cache scopes, marking a scope as dynamic has no effect,\n        // because the outer cache scope creates a cache boundary. This is\n        // subtly different from reading a dynamic data source, which is\n        // forbidden inside a cache scope.\n        return\n      case 'private-cache':\n        // A private cache scope is already dynamic by definition.\n        return\n      case 'prerender-legacy':\n      case 'prerender-ppr':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        // We aren't prerendering, but we are generating a static page. We need\n        // to bail out of static generation.\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(workUnitStore: WorkUnitStore) {\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache':\n      // Inside cache scopes, marking a scope as dynamic has no effect,\n      // because the outer cache scope creates a cache boundary. This is\n      // subtly different from reading a dynamic data source, which is\n      // forbidden inside a cache scope.\n      return\n    case 'private-cache':\n      // A private cache scope is already dynamic by definition.\n      return\n    case 'prerender':\n    case 'prerender-runtime':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    case 'prerender-client':\n      break\n    case 'request':\n      if (process.env.NODE_ENV !== 'production') {\n        workUnitStore.usedDynamic = true\n      }\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */\nexport function warnOnSyncDynamicError(dynamicTracking: DynamicTrackingState) {\n  if (dynamicTracking.syncDynamicErrorWithStack) {\n    // the server did something sync dynamic, likely\n    // leading to an early termination of the prerender.\n    console.error(dynamicTracking.syncDynamicErrorWithStack)\n  }\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createRenderInBrowserAbortSignal(): AbortSignal {\n  const controller = new AbortController()\n  controller.abort(new BailoutToCSRError('Render in Browser'))\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: WorkUnitStore\n): AbortSignal | undefined {\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime':\n      const controller = new AbortController()\n\n      if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If\n        // the input we're waiting on is coming from another cache, we do want\n        // to wait for it so that we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(() => {\n          controller.abort()\n        })\n      } else {\n        // Otherwise we're in the final render and we should already have all\n        // our caches filled.\n        // If the prerender uses stages, we have wait until the runtime stage,\n        // at which point all runtime inputs will be resolved.\n        // (otherwise, a runtime prerender might consider `cookies()` hanging\n        //  even though they'd resolve in the next task.)\n        //\n        // We might still be waiting on some microtasks so we\n        // wait one tick before giving up. When we give up, we still want to\n        // render the content of this cache as deeply as we can so that we can\n        // suspend as deeply as possible in the tree or not at all if we don't\n        // end up waiting for the input.\n        const runtimeStagePromise = getRuntimeStagePromise(workUnitStore)\n        if (runtimeStagePromise) {\n          runtimeStagePromise.then(() =>\n            scheduleOnNextTick(() => controller.abort())\n          )\n        } else {\n          scheduleOnNextTick(() => controller.abort())\n        }\n      }\n\n      return controller.signal\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return undefined\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workStore && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n      case 'prerender': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          // We are in a prerender with cacheComponents semantics. We are going to\n          // hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole.\n          React.use(\n            makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          )\n        }\n        break\n      }\n      case 'prerender-ppr': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        }\n        break\n      }\n      case 'prerender-runtime':\n        throw new InvariantError(\n          `\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'cache':\n      case 'private-cache':\n        throw new InvariantError(\n          `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'prerender-legacy':\n      case 'request':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\n\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags =\n  'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6'\n\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(\n  `\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`\n)\n\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (\n    hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(\n      componentStack\n    )\n  ) {\n    // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n    // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n    // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nexport function logDisallowedDynamicError(\n  workStore: WorkStore,\n  error: Error\n): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n\nexport function delayUntilRuntimeStage<T>(\n  prerenderStore: PrerenderStoreModernRuntime,\n  result: Promise<T>\n): Promise<T> {\n  if (prerenderStore.runtimeStagePromise) {\n    return prerenderStore.runtimeStagePromise.then(() => result)\n  }\n  return result\n}\n"], "names": ["Postpone", "PreludeState", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createRenderInBrowserAbortSignal", "delayUntilRuntimeStage", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "logDisallowedDynamicError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "warnOnSyncDynamicError", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "signal", "aborted", "console", "workUnitAsyncStorage", "getStore", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "BailoutToCSRError", "cacheSignal", "inputReady", "then", "runtimeStagePromise", "getRuntimeStagePromise", "scheduleOnNextTick", "workStore", "workAsyncStorage", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "InvariantError", "hasSuspenseRegex", "bodyAndImplicitTags", "hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex", "RegExp", "ROOT_LAYOUT_BOUNDARY_NAME", "hasMetadataRegex", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "dev", "hasReadableErrorStacks", "prelude", "i", "result"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6VeA,QAAQ,EAAA;eAARA;;IA4XJC,YAAY,EAAA;eAAZA;;IApbIC,2CAA2C,EAAA;eAA3CA;;IArCAC,kCAAkC,EAAA;eAAlCA;;IAuLAC,mBAAmB,EAAA;eAAnBA;;IAkIAC,qBAAqB,EAAA;eAArBA;;IA5HAC,oBAAoB,EAAA;eAApBA;;IArXAC,0BAA0B,EAAA;eAA1BA;;IAUAC,4BAA4B,EAAA;eAA5BA;;IAmbAC,6BAA6B,EAAA;eAA7BA;;IAXAC,gCAAgC,EAAA;eAAhCA;;IA8TAC,sBAAsB,EAAA;eAAtBA;;IAhXAC,wBAAwB,EAAA;eAAxBA;;IA5WAC,qBAAqB,EAAA;eAArBA;;IAsSAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IAqTAC,yBAAyB,EAAA;eAAzBA;;IAtnBAC,yBAAyB,EAAA;eAAzBA;;IA6PAC,oBAAoB,EAAA;eAApBA;;IA4YAC,wBAAwB,EAAA;eAAxBA;;IA/jBAC,gCAAgC,EAAA;eAAhCA;;IAueAC,yBAAyB,EAAA;eAAzBA;;IA9cAC,+BAA+B,EAAA;eAA/BA;;IAuEAC,qCAAqC,EAAA;eAArCA;;IAgEHC,sCAAsC,EAAA;eAAtCA;;IAqPGC,qBAAqB,EAAA;eAArBA;;IA9PAC,sBAAsB,EAAA;eAAtBA;;;8DA9TE;oCAEiB;yCACG;8CAI/B;0CAC0B;uCACE;mCAM5B;2BAC4B;8BACD;gCACH;;;;;;AAE/B,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AAwChD,SAAStB,2BACduB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASxB,sBACdyB,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcP,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCO,gCAAkCC,UAAU;AACrD;AASO,SAAStB,0BACduB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,kEAAkE;gBAClE,gEAAgE;gBAChE,kCAAkC;gBAClC;YACF,KAAK;gBACH,0DAA0D;gBAC1D;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACED;QACJ;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAID,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;gBACH,OAAOxB,qBACLsB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;YAEjC,KAAK;gBACHP,cAAcQ,UAAU,GAAG;gBAE3B,uEAAuE;gBACvE,oCAAoC;gBACpC,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAC,MAAMY,uBAAuB,GAAGb;gBAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;gBAEnC,MAAMJ;YACR,KAAK;gBACH,IAAIK,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;oBACzChB,cAAciB,WAAW,GAAG;gBAC9B;gBACA;YACF;gBACEjB;QACJ;IACF;AACF;AAQO,SAASrB,iCACdmB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS5B,gCAAgCmB,aAA4B;IAC1E,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,kEAAkE;YAClE,gEAAgE;YAChE,kCAAkC;YAClC;QACF,KAAK;YACH,0DAA0D;YAC1D;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH;QACF,KAAK;YACH,IAAIa,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzChB,cAAciB,WAAW,GAAG;YAC9B;YACA;QACF;YACEjB;IACJ;AACF;AAEA,SAASmB,oCACPb,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAME,SAAS,CAAC,MAAM,EAAEd,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAMuB,QAAQC,gCAAgCF;IAE9CF,eAAeK,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMd,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACmC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfZ,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIqC,QAAQb,KAAK,GACjBc;YACJ7B;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClB8B,cAAqB,EACrBV,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDY,oCAAoCb,OAAOR,YAAYoB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBhB,yBAAyB,GAAGqC;QAC9C;IACF;AACF;AAEO,SAAS9C,sCACd+C,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASrE,4CACd6C,KAAa,EACbR,UAAkB,EAClB8B,cAAqB,EACrBV,cAAoC;IAEpC,MAAMa,kBAAkBb,eAAeK,UAAU,CAACS,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1Bd,oCAAoCb,OAAOR,YAAYoB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;gBACtDgB,gBAAgBhB,yBAAyB,GAAGqC;YAC9C;QACF;IACF;IACA,MAAMN,gCACJ,CAAC,MAAM,EAAEhB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AASO,SAASb,uBAAuBsB,eAAqC;IAC1E,IAAIA,gBAAgBhB,yBAAyB,EAAE;QAC7C,gDAAgD;QAChD,oDAAoD;QACpD2C,QAAQb,KAAK,CAACd,gBAAgBhB,yBAAyB;IACzD;AACF;AAGO,MAAMR,yCACXD;AASK,SAASvB,SAAS,EAAE6D,MAAM,EAAEd,KAAK,EAAiB;IACvD,MAAMY,iBAAiBiB,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAM7B,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACN9B,qBAAqB6B,OAAOc,QAAQb;AACtC;AAEO,SAAS9B,qBACd6B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C8B;IACA,IAAI9B,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACmC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfZ,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIqC,QAAQb,KAAK,GACjBc;YACJ7B;QACF;IACF;IAEAX,OAAAA,OAAK,CAACC,iBAAiB,CAACkD,qBAAqBhC,OAAOR;AACtD;AAEA,SAASwC,qBAAqBhC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAASzB,kBAAkBoC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY8B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyB/B,IAAY8B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBpB,MAAc;IAC7C,OACEA,OAAOqB,QAAQ,CACb,sEAEFrB,OAAOqB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIZ,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMgB,6BAA6B;AAEnC,SAASpB,gCAAgCiB,OAAe;IACtD,MAAMlB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMa,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BlB,MAAcsB,MAAM,GAAGD;IACzB,OAAOrB;AACT;AAMO,SAAS/C,4BACd+C,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAcsB,MAAM,KAAKD,8BAC1B,UAAUrB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAAS/D,oBACd2B,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS/E,qBACdgF,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACmC,IAAI,IAAIqB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASnB,yBACdmB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOnC,KAAK,KAAK,YAAYmC,OAAOnC,KAAK,CAAC+B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEnD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLqC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAEvD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASwB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAIwC,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAASzD;IACd,MAAMsD,aAAa,IAAI+B;IACvB/B,WAAWC,KAAK,CAAC,OAAA,cAA0C,CAA1C,IAAI+B,cAAAA,iBAAiB,CAAC,sBAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAAyC;IAC1D,OAAOhC,WAAWS,MAAM;AAC1B;AAOO,SAAShE,8BACdgC,aAA4B;IAE5B,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,MAAMsB,aAAa,IAAI+B;YAEvB,IAAItD,cAAcwD,WAAW,EAAE;gBAC7B,sEAAsE;gBACtE,sEAAsE;gBACtE,8DAA8D;gBAC9DxD,cAAcwD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;oBAC1CnC,WAAWC,KAAK;gBAClB;YACF,OAAO;gBACL,qEAAqE;gBACrE,qBAAqB;gBACrB,sEAAsE;gBACtE,sDAAsD;gBACtD,qEAAqE;gBACrE,iDAAiD;gBACjD,EAAE;gBACF,qDAAqD;gBACrD,oEAAoE;gBACpE,sEAAsE;gBACtE,sEAAsE;gBACtE,gCAAgC;gBAChC,MAAMmC,sBAAsBC,CAAAA,GAAAA,8BAAAA,sBAAsB,EAAC5D;gBACnD,IAAI2D,qBAAqB;oBACvBA,oBAAoBD,IAAI,CAAC,IACvBG,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMtC,WAAWC,KAAK;gBAE7C,OAAO;oBACLqC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMtC,WAAWC,KAAK;gBAC3C;YACF;YAEA,OAAOD,WAAWS,MAAM;QAC1B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOL;QACT;YACE3B;IACJ;AACF;AAEO,SAASpC,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBjB,eAAe,CAACmC,IAAI,CAAC;YACnCZ,OAAON,gBAAgBlB,sBAAsB,GACzC,IAAIqC,QAAQb,KAAK,GACjBc;YACJ7B;QACF;IACF;AACF;AAEO,SAASd,sBAAsBc,UAAkB;IACtD,MAAMgE,YAAYC,0BAAAA,gBAAgB,CAAC3B,QAAQ;IAC3C,MAAMpC,gBAAgBmC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAI0B,aAAa9D,eAAe;QAC9B,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBAAa;oBAChB,MAAM+D,iBAAiBhE,cAAciE,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,wEAAwE;wBACxE,6DAA6D;wBAC7D,wDAAwD;wBACxD/E,OAAAA,OAAK,CAACgF,GAAG,CACPC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChBpE,cAAcqE,YAAY,EAC1BP,UAAUxD,KAAK,EACfR;oBAGN;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpB,MAAMkE,iBAAiBhE,cAAciE,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,OAAOzF,qBACLqF,UAAUxD,KAAK,EACfR,YACAE,cAAcO,eAAe;oBAEjC;oBACA;gBACF;YACA,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAI+D,gBAAAA,cAAc,CACtB,CAAC,EAAE,EAAExE,WAAW,uEAAuE,EAAEA,WAAW,+EAA+E,CAAC,GADhL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIwE,gBAAAA,cAAc,CACtB,CAAC,EAAE,EAAExE,WAAW,iEAAiE,EAAEA,WAAW,+EAA+E,CAAC,GAD1K,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEE;QACJ;IACF;AACF;AAEA,MAAMuE,mBAAmB;AAEzB,uFAAuF;AACvF,MAAMC,sBACJ;AAEF,2EAA2E;AAC3E,+EAA+E;AAC/E,4FAA4F;AAC5F,EAAE;AACF,mBAAmB;AACnB,8BAA8B;AAC9B,mDAAmD;AACnD,EAAE;AACF,yEAAyE;AACzE,8BAA8B;AAC9B,mCAAmC;AACnC,mDAAmD;AACnD,MAAMC,4DAA4D,IAAIC,OACpE,CAAC,uDAAuD,EAAEF,oBAAoB,yCAAyC,EAAEG,mBAAAA,yBAAyB,CAAC,cAAc,CAAC;AAGpK,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIJ,OAC3B,CAAC,UAAU,EAAEK,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIN,OAAO,CAAC,UAAU,EAAEO,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAASrG,0BACdkF,SAAoB,EACpBoB,cAAsB,EACtBC,iBAAyC,EACzCrC,aAAmC;IAEnC,IAAIkC,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIN,iBAAiBQ,IAAI,CAACF,iBAAiB;QAChDC,kBAAkB1F,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIqF,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBzF,kBAAkB,GAAG;QACvC;IACF,OAAO,IACL+E,0DAA0DW,IAAI,CAC5DF,iBAEF;QACA,+GAA+G;QAC/G,sGAAsG;QACtG,wGAAwG;QACxGC,kBAAkBxF,iBAAiB,GAAG;QACtCwF,kBAAkB3F,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI+E,iBAAiBa,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkBxF,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAImD,cAAcvD,yBAAyB,EAAE;QAClD,qDAAqD;QACrD4F,kBAAkBvF,aAAa,CAAC6B,IAAI,CAClCqB,cAAcvD,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAMgD,UAAU,CAAC,OAAO,EAAEuB,UAAUxD,KAAK,CAAC,2NAA2N,CAAC;QACtQ,MAAMe,QAAQgE,qCAAqC9C,SAAS2C;QAC5DC,kBAAkBvF,aAAa,CAAC6B,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASgE,qCACP9C,OAAe,EACf2C,cAAsB;IAEtB,MAAMI,aACJxE,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB7B,OAAAA,OAAK,CAACoG,iBAAiB,GAC5DpG,OAAAA,OAAK,CAACoG,iBAAiB,KACvB;IAEN,MAAMlE,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMa,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BlB,MAAMR,KAAK,GAAGQ,MAAMmE,IAAI,GAAG,OAAOjD,UAAW+C,CAAAA,cAAcJ,cAAa;IACxE,OAAO7D;AACT;AAEO,IAAK7D,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;;AAML,SAASe,0BACduF,SAAoB,EACpBzC,KAAY;IAEZa,QAAQb,KAAK,CAACA;IAEd,IAAI,CAACyC,UAAU2B,GAAG,EAAE;QAClB,IAAI3B,UAAU4B,sBAAsB,EAAE;YACpCxD,QAAQb,KAAK,CACX,CAAC,iIAAiI,EAAEyC,UAAUxD,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACL4B,QAAQb,KAAK,CAAC,CAAC;0EACqD,EAAEyC,UAAUxD,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS5B,yBACdoF,SAAoB,EACpB6B,OAAqB,EACrBR,iBAAyC,EACzCtC,aAAmC;IAEnC,IAAI8C,YAAAA,GAA+B;QACjC,IAAIR,kBAAkB3F,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,IAAIqD,cAActD,yBAAyB,EAAE;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,gEAAgE;YAChEhB,0BACEuF,WACAjB,cAActD,yBAAyB;YAEzC,MAAM,IAAIc,yBAAAA,qBAAqB;QACjC;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMT,gBAAgBuF,kBAAkBvF,aAAa;QACrD,IAAIA,cAAcgD,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAIgD,IAAI,GAAGA,IAAIhG,cAAcgD,MAAM,EAAEgD,IAAK;gBAC7CrH,0BAA0BuF,WAAWlE,aAAa,CAACgG,EAAE;YACvD;YAEA,MAAM,IAAIvF,yBAAAA,qBAAqB;QACjC;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAI8E,kBAAkBzF,kBAAkB,EAAE;YACxCwC,QAAQb,KAAK,CACX,CAAC,OAAO,EAAEyC,UAAUxD,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;QAEA,IAAIsF,YAAAA,GAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3CzD,QAAQb,KAAK,CACX,CAAC,OAAO,EAAEyC,UAAUxD,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;IACF,OAAO;QACL,IACE8E,kBAAkBxF,iBAAiB,KAAK,SACxCwF,kBAAkB1F,kBAAkB,EACpC;YACAyC,QAAQb,KAAK,CACX,CAAC,OAAO,EAAEyC,UAAUxD,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,IAAID,yBAAAA,qBAAqB;QACjC;IACF;AACF;AAEO,SAASnC,uBACdgD,cAA2C,EAC3C2E,MAAkB;IAElB,IAAI3E,eAAeyC,mBAAmB,EAAE;QACtC,OAAOzC,eAAeyC,mBAAmB,CAACD,IAAI,CAAC,IAAMmC;IACvD;IACA,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,yIACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/server/dev/hot-reloader-types.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { UrlObject } from 'url'\nimport type { Duplex } from 'stream'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type getBaseWebpackConfig from '../../build/webpack-config'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { Project, Update as TurbopackUpdate } from '../../build/swc/types'\nimport type { VersionInfo } from './parse-version-info'\nimport type { DebugInfo } from '../../next-devtools/shared/types'\nimport type { DevIndicatorServerState } from './dev-indicator-server-state'\nimport type { DevToolsConfig } from '../../next-devtools/dev-overlay/shared'\n\nexport const enum HMR_ACTIONS_SENT_TO_BROWSER {\n  ADDED_PAGE = 'addedPage',\n  REMOVED_PAGE = 'removedPage',\n  RELOAD_PAGE = 'reloadPage',\n  SERVER_COMPONENT_CHANGES = 'serverComponentChanges',\n  MIDDLEWARE_CHANGES = 'middlewareChanges',\n  CLIENT_CHANGES = 'clientChanges',\n  SERVER_ONLY_CHANGES = 'serverOnlyChanges',\n  SYNC = 'sync',\n  BUILT = 'built',\n  BUILDING = 'building',\n  DEV_PAGES_MANIFEST_UPDATE = 'devPagesManifestUpdate',\n  TURBOPACK_MESSAGE = 'turbopack-message',\n  SERVER_ERROR = 'serverError',\n  TURBOPACK_CONNECTED = 'turbopack-connected',\n  ISR_MANIFEST = 'isrManifest',\n  DEV_INDICATOR = 'devIndicator',\n  DEVTOOLS_CONFIG = 'devtoolsConfig',\n}\n\ninterface ServerErrorAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR\n  errorJSON: string\n}\n\nexport interface TurbopackMessageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE\n  data: TurbopackUpdate | TurbopackUpdate[]\n}\n\ninterface BuildingAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING\n}\n\nexport interface CompilationError {\n  moduleName?: string\n  message: string\n  details?: string\n  moduleTrace?: Array<{ moduleName?: string }>\n  stack?: string\n}\nexport interface SyncAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  versionInfo: VersionInfo\n  updatedModules?: ReadonlyArray<string>\n  debug?: DebugInfo\n  devIndicator: DevIndicatorServerState\n  devToolsConfig?: DevToolsConfig\n}\ninterface BuiltAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT\n  hash: string\n  errors: ReadonlyArray<CompilationError>\n  warnings: ReadonlyArray<CompilationError>\n  updatedModules?: ReadonlyArray<string>\n}\n\ninterface AddedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ADDED_PAGE\n  data: [page: string | null]\n}\n\ninterface RemovedPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.REMOVED_PAGE\n  data: [page: string | null]\n}\n\nexport interface ReloadPageAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE\n  data: string\n}\n\ninterface ServerComponentChangesAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES\n  hash: string\n}\n\ninterface MiddlewareChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES\n}\n\ninterface ClientChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES\n}\n\ninterface ServerOnlyChangesAction {\n  event: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES\n  pages: ReadonlyArray<string>\n}\n\ninterface DevPagesManifestUpdateAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE\n  data: [\n    {\n      devPagesManifest: true\n    },\n  ]\n}\n\nexport interface TurbopackConnectedAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n  data: { sessionId: number }\n}\n\nexport interface AppIsrManifestAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST\n  data: Record<string, boolean>\n}\n\nexport interface DevToolsConfigAction {\n  action: HMR_ACTIONS_SENT_TO_BROWSER.DEVTOOLS_CONFIG\n  data: DevToolsConfig\n}\n\nexport type HMR_ACTION_TYPES =\n  | TurbopackMessageAction\n  | TurbopackConnectedAction\n  | BuildingAction\n  | SyncAction\n  | BuiltAction\n  | AddedPageAction\n  | RemovedPageAction\n  | ReloadPageAction\n  | ServerComponentChangesAction\n  | ClientChangesAction\n  | MiddlewareChangesAction\n  | ServerOnlyChangesAction\n  | DevPagesManifestUpdateAction\n  | ServerErrorAction\n  | AppIsrManifestAction\n  | DevToolsConfigAction\n\nexport type TurbopackMsgToBrowser =\n  | { type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE; data: any }\n  | {\n      type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      data: { sessionId: number }\n    }\n\nexport interface NextJsHotReloaderInterface {\n  turbopackProject?: Project\n  activeWebpackConfigs?: Array<Awaited<ReturnType<typeof getBaseWebpackConfig>>>\n  serverStats: webpack.Stats | null\n  edgeServerStats: webpack.Stats | null\n  run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlObject\n  ): Promise<{ finished?: true }>\n\n  setHmrServerError(error: Error | null): void\n  clearHmrServerError(): void\n  start(): Promise<void>\n  send(action: HMR_ACTION_TYPES): void\n  getCompilationErrors(page: string): Promise<any[]>\n  onHMR(\n    req: IncomingMessage,\n    _socket: Duplex,\n    head: Buffer,\n    onUpgrade: (client: { send(data: string): void }) => void\n  ): void\n  invalidate({\n    reloadAfterInvalidation,\n  }: {\n    reloadAfterInvalidation: boolean\n  }): Promise<void> | void\n  buildFallbackError(): Promise<void>\n  ensurePage({\n    page,\n    clientOnly,\n    appPaths,\n    definition,\n    isApp,\n    url,\n  }: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    isApp?: boolean\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void>\n  close(): void\n}\n"], "names": ["HMR_ACTIONS_SENT_TO_BROWSER"], "mappings": ";;;+BAYkBA,+BAAAA;;;eAAAA;;;AAAX,IAAWA,8BAAAA,WAAAA,GAAAA,SAAAA,2BAAAA;;;;;;;;;;;;;;;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;;;;;;;;;;;;;;;;IAQzBA,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,IAAIH,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAoaaA,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/parse-path.ts"], "sourcesContent": ["/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n"], "names": ["parsePath", "path", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "substring", "query", "undefined", "hash", "slice"], "mappings": "AAAA;;;;CAIC;;;+BACeA,aAAAA;;;eAAAA;;;AAAT,SAASA,UAAUC,IAAY;IACpC,MAAMC,YAAYD,KAAKE,OAAO,CAAC;IAC/B,MAAMC,aAAaH,KAAKE,OAAO,CAAC;IAChC,MAAME,WAAWD,aAAa,CAAC,KAAMF,CAAAA,YAAY,KAAKE,aAAaF,SAAQ;IAE3E,IAAIG,YAAYH,YAAY,CAAC,GAAG;QAC9B,OAAO;YACLI,UAAUL,KAAKM,SAAS,CAAC,GAAGF,WAAWD,aAAaF;YACpDM,OAAOH,WACHJ,KAAKM,SAAS,CAACH,YAAYF,YAAY,CAAC,IAAIA,YAAYO,aACxD;YACJC,MAAMR,YAAY,CAAC,IAAID,KAAKU,KAAK,CAACT,aAAa;QACjD;IACF;IAEA,OAAO;QAAEI,UAAUL;QAAMO,OAAO;QAAIE,MAAM;IAAG;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/add-path-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n"], "names": ["addPathPrefix", "path", "prefix", "startsWith", "pathname", "query", "hash", "parsePath"], "mappings": ";;;+BAMgBA,iBAAAA;;;eAAAA;;;2BANU;AAMnB,SAASA,cAAcC,IAAY,EAAEC,MAAe;IACzD,IAAI,CAACD,KAAKE,UAAU,CAAC,QAAQ,CAACD,QAAQ;QACpC,OAAOD;IACT;IAEA,MAAM,EAAEG,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACN;IAC5C,OAAQ,KAAEC,SAASE,WAAWC,QAAQC;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/remove-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n"], "names": ["removeTrailingSlash", "route", "replace"], "mappings": "AAAA;;;;;;CAMC;;;+BACeA,uBAAAA;;;eAAAA;;;AAAT,SAASA,oBAAoBC,KAAa;IAC/C,OAAOA,MAAMC,OAAO,CAAC,OAAO,OAAO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": ";;;+BAWS<PERSON>,YAAAA;;;eAAAA;;;AAXT,IAAIA,WAAW,CAACC,KAAe;AAC/B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC;;;+BACeA,cAAAA;;;eAAAA;;;AAAT,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1401, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "hexHash", "str", "hash", "i", "length", "char", "charCodeAt", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;;;;;;;;;;;IACxCA,QAAQ,EAAA;eAARA;;IASAC,OAAO,EAAA;eAAPA;;;AATT,SAASD,SAASE,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASF,QAAQC,GAAW;IACjC,OAAOF,SAASE,KAAKM,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/cache-busting-search-param.ts"], "sourcesContent": ["import { hexHash } from '../../hash'\n\nexport function computeCacheBustingSearchParam(\n  prefetchHeader: '1' | '2' | '0' | undefined,\n  segmentPrefetchHeader: string | string[] | undefined,\n  stateTreeHeader: string | string[] | undefined,\n  nextUrlHeader: string | string[] | undefined\n): string {\n  if (\n    (prefetchHeader === undefined || prefetchHeader === '0') &&\n    segmentPrefetchHeader === undefined &&\n    stateTreeHeader === undefined &&\n    nextUrlHeader === undefined\n  ) {\n    return ''\n  }\n  return hexHash(\n    [\n      prefetchHeader || '0',\n      segmentPrefetchHeader || '0',\n      stateTreeHeader || '0',\n      nextUrlHeader || '0',\n    ].join(',')\n  )\n}\n"], "names": ["computeCacheBustingSearchParam", "prefetch<PERSON><PERSON><PERSON>", "segmentPrefetchHeader", "stateTreeHeader", "nextUrl<PERSON><PERSON>er", "undefined", "hexHash", "join"], "mappings": ";;;+BAEgBA,kCAAAA;;;eAAAA;;;sBAFQ;AAEjB,SAASA,+BACdC,cAA2C,EAC3CC,qBAAoD,EACpDC,eAA8C,EAC9CC,aAA4C;IAE5C,IACGH,CAAAA,mBAAmBI,aAAaJ,mBAAmB,GAAE,KACtDC,0BAA0BG,aAC1BF,oBAAoBE,aACpBD,kBAAkBC,WAClB;QACA,OAAO;IACT;IACA,OAAOC,CAAAA,GAAAA,MAAAA,OAAO,EACZ;QACEL,kBAAkB;QAClBC,yBAAyB;QACzBC,mBAAmB;QACnBC,iBAAiB;KAClB,CAACG,IAAI,CAAC;AAEX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/segment-cache/segment-value-encoding.ts"], "sourcesContent": ["import { PAGE_SEGMENT_KEY } from '../segment'\nimport type { Segment as FlightRouterStateSegment } from '../../../server/app-render/types'\n\n// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\nexport type SegmentRequestKeyPart = Opaque<'SegmentRequestKeyPart', string>\nexport type SegmentRequestKey = Opaque<'SegmentRequestKey', string>\nexport type SegmentCacheKeyPart = Opaque<'SegmentCacheKeyPart', string>\nexport type SegmentCacheKey = Opaque<'SegmentCacheKey', string>\n\nexport const ROOT_SEGMENT_REQUEST_KEY = '' as SegmentRequestKey\nexport const ROOT_SEGMENT_CACHE_KEY = '' as SegmentCacheKey\n\nexport function createSegmentRequestKeyPart(\n  segment: FlightRouterStateSegment\n): SegmentRequestKeyPart {\n  if (typeof segment === 'string') {\n    if (segment.startsWith(PAGE_SEGMENT_KEY)) {\n      // The Flight Router State type sometimes includes the search params in\n      // the page segment. However, the Segment Cache tracks this as a separate\n      // key. So, we strip the search params here, and then add them back when\n      // the cache entry is turned back into a FlightRouterState. This is an\n      // unfortunate consequence of the FlightRouteState being used both as a\n      // transport type and as a cache key; we'll address this once more of the\n      // Segment Cache implementation has settled.\n      // TODO: We should hoist the search params out of the FlightRouterState\n      // type entirely, This is our plan for dynamic route params, too.\n      return PAGE_SEGMENT_KEY as SegmentRequestKeyPart\n    }\n    const safeName =\n      // TODO: FlightRouterState encodes Not Found routes as \"/_not-found\".\n      // But params typically don't include the leading slash. We should use\n      // a different encoding to avoid this special case.\n      segment === '/_not-found'\n        ? '_not-found'\n        : encodeToFilesystemAndURLSafeString(segment)\n    // Since this is not a dynamic segment, it's fully encoded. It does not\n    // need to be \"hydrated\" with a param value.\n    return safeName as SegmentRequestKeyPart\n  }\n\n  const name = segment[0]\n  const paramType = segment[2]\n  const safeName = encodeToFilesystemAndURLSafeString(name)\n\n  const encodedName = '$' + paramType + '$' + safeName\n  return encodedName as SegmentRequestKeyPart\n}\n\nexport function appendSegmentRequestKeyPart(\n  parentRequestKey: SegmentRequestKey,\n  parallelRouteKey: string,\n  childRequestKeyPart: SegmentRequestKeyPart\n): SegmentRequestKey {\n  // Aside from being filesystem safe, segment keys are also designed so that\n  // each segment and parallel route creates its own subdirectory. Roughly in\n  // the same shape as the source app directory. This is mostly just for easier\n  // debugging (you can open up the build folder and navigate the output); if\n  // we wanted to do we could just use a flat structure.\n\n  // Omit the parallel route key for children, since this is the most\n  // common case. Saves some bytes (and it's what the app directory does).\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? childRequestKeyPart\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${childRequestKeyPart}`\n  return (parentRequestKey + '/' + slotKey) as SegmentRequestKey\n}\n\nexport function createSegmentCacheKeyPart(\n  requestKeyPart: SegmentRequestKeyPart,\n  segment: FlightRouterStateSegment\n): SegmentCacheKeyPart {\n  if (typeof segment === 'string') {\n    return requestKeyPart as any as SegmentCacheKeyPart\n  }\n  const paramValue = segment[1]\n  const safeValue = encodeToFilesystemAndURLSafeString(paramValue)\n  return (requestKeyPart + '$' + safeValue) as SegmentCacheKeyPart\n}\n\nexport function appendSegmentCacheKeyPart(\n  parentSegmentKey: SegmentCacheKey,\n  parallelRouteKey: string,\n  childCacheKeyPart: SegmentCacheKeyPart\n): SegmentCacheKey {\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? childCacheKeyPart\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${childCacheKeyPart}`\n  return (parentSegmentKey + '/' + slotKey) as SegmentCacheKey\n}\n\n// Define a regex pattern to match the most common characters found in a route\n// param. It excludes anything that might not be cross-platform filesystem\n// compatible, like |. It does not need to be precise because the fallback is to\n// just base64url-encode the whole parameter, which is fine; we just don't do it\n// by default for compactness, and for easier debugging.\nconst simpleParamValueRegex = /^[a-zA-Z0-9\\-_@]+$/\n\nfunction encodeToFilesystemAndURLSafeString(value: string) {\n  if (simpleParamValueRegex.test(value)) {\n    return value\n  }\n  // If there are any unsafe characters, base64url-encode the entire value.\n  // We also add a ! prefix so it doesn't collide with the simple case.\n  const base64url = btoa(value)\n    .replace(/\\+/g, '-') // Replace '+' with '-'\n    .replace(/\\//g, '_') // Replace '/' with '_'\n    .replace(/=+$/, '') // Remove trailing '='\n  return '!' + base64url\n}\n\nexport function convertSegmentPathToStaticExportFilename(\n  segmentPath: string\n): string {\n  return `__next${segmentPath.replace(/\\//g, '.')}.txt`\n}\n"], "names": ["ROOT_SEGMENT_CACHE_KEY", "ROOT_SEGMENT_REQUEST_KEY", "appendSegmentCacheKeyPart", "appendSegmentRequestKeyPart", "convertSegmentPathToStaticExportFilename", "createSegmentCacheKeyPart", "createSegmentRequestKeyPart", "segment", "startsWith", "PAGE_SEGMENT_KEY", "safeName", "encodeToFilesystemAndURLSafeString", "name", "paramType", "encodedName", "parentRequestKey", "parallelRouteKey", "childRequestKeyPart", "<PERSON><PERSON><PERSON>", "requestKeyPart", "paramValue", "safeValue", "parentSegmentKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simpleParamValueRegex", "value", "test", "base64url", "btoa", "replace", "segmentPath"], "mappings": ";;;;;;;;;;;;;;;;;;;IAYaA,sBAAsB,EAAA;eAAtBA;;IADAC,wBAAwB,EAAA;eAAxBA;;IAuEGC,yBAAyB,EAAA;eAAzBA;;IAhCAC,2BAA2B,EAAA;eAA3BA;;IAgEAC,wCAAwC,EAAA;eAAxCA;;IA5CAC,yBAAyB,EAAA;eAAzBA;;IAxDAC,2BAA2B,EAAA;eAA3BA;;;yBAdiB;AAW1B,MAAML,2BAA2B;AACjC,MAAMD,yBAAyB;AAE/B,SAASM,4BACdC,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,IAAIA,QAAQC,UAAU,CAACC,SAAAA,gBAAgB,GAAG;YACxC,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,4CAA4C;YAC5C,uEAAuE;YACvE,iEAAiE;YACjE,OAAOA,SAAAA,gBAAgB;QACzB;QACA,MAAMC,WACJ,AACA,qEADqE,CACC;QACtE,mDAAmD;QACnDH,YAAY,gBACR,eACAI,mCAAmCJ;QACzC,uEAAuE;QACvE,4CAA4C;QAC5C,OAAOG;IACT;IAEA,MAAME,OAAOL,OAAO,CAAC,EAAE;IACvB,MAAMM,YAAYN,OAAO,CAAC,EAAE;IAC5B,MAAMG,WAAWC,mCAAmCC;IAEpD,MAAME,cAAc,MAAMD,YAAY,MAAMH;IAC5C,OAAOI;AACT;AAEO,SAASX,4BACdY,gBAAmC,EACnCC,gBAAwB,EACxBC,mBAA0C;IAE1C,2EAA2E;IAC3E,2EAA2E;IAC3E,6EAA6E;IAC7E,2EAA2E;IAC3E,sDAAsD;IAEtD,mEAAmE;IACnE,wEAAwE;IACxE,MAAMC,UACJF,qBAAqB,aACjBC,sBACC,MAAGN,mCAAmCK,oBAAkB,MAAGC;IAClE,OAAQF,mBAAmB,MAAMG;AACnC;AAEO,SAASb,0BACdc,cAAqC,EACrCZ,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAOY;IACT;IACA,MAAMC,aAAab,OAAO,CAAC,EAAE;IAC7B,MAAMc,YAAYV,mCAAmCS;IACrD,OAAQD,iBAAiB,MAAME;AACjC;AAEO,SAASnB,0BACdoB,gBAAiC,EACjCN,gBAAwB,EACxBO,iBAAsC;IAEtC,MAAML,UACJF,qBAAqB,aACjBO,oBACC,MAAGZ,mCAAmCK,oBAAkB,MAAGO;IAClE,OAAQD,mBAAmB,MAAMJ;AACnC;AAEA,8EAA8E;AAC9E,0EAA0E;AAC1E,gFAAgF;AAChF,gFAAgF;AAChF,wDAAwD;AACxD,MAAMM,wBAAwB;AAE9B,SAASb,mCAAmCc,KAAa;IACvD,IAAID,sBAAsBE,IAAI,CAACD,QAAQ;QACrC,OAAOA;IACT;IACA,yEAAyE;IACzE,qEAAqE;IACrE,MAAME,YAAYC,KAAKH,OACpBI,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,IAAI,sBAAsB;;IAC5C,OAAO,MAAMF;AACf;AAEO,SAASvB,yCACd0B,WAAmB;IAEnB,OAAQ,WAAQA,YAAYD,OAAO,CAAC,OAAO,OAAK;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1642, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC;;;+BACeA,sBAAAA;;;eAAAA;;;AAAT,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["normalizeAppPath", "normalizeRscURL", "route", "ensureLeadingSlash", "split", "reduce", "pathname", "segment", "index", "segments", "isGroupSegment", "length", "url", "replace"], "mappings": ";;;;;;;;;;;;;;IAsBgBA,gBAAgB,EAAA;eAAhBA;;IAmCAC,eAAe,EAAA;eAAfA;;;oCAzDmB;yBACJ;AAqBxB,SAASD,iBAAiBE,KAAa;IAC5C,OAAOC,CAAAA,GAAAA,oBAAAA,kBAAkB,EACvBD,MAAME,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,IAAII,CAAAA,GAAAA,SAAAA,cAAc,EAACH,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASE,MAAM,GAAG,GAC5B;YACA,OAAOL;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASN,gBAAgBW,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1712, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;IAGaA,0BAA0B,EAAA;eAA1BA;;IAkBGC,mCAAmC,EAAA;eAAnCA;;IAXAC,0BAA0B,EAAA;eAA1BA;;;0BAViB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASE,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASR,oCAAoCE,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BV,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAO,oBAAoBI,CAAAA,GAAAA,UAAAA,gBAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BV,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIN,MACP,iCAA8BV,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIJ,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1819, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\n// Note: The pattern [\\w-]+-Google captures all Google crawlers with \"-Google\" suffix (e.g., Mediapartners-Google, AdsBot-Google, Storebot-Google)\n// as well as crawlers starting with \"Google-\" (e.g., Google-PageRenderer, Google-InspectionTool)\nexport const HTML_LIMITED_BOT_UA_RE =\n  /[\\w-]+-Google|Google-[\\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;AACtK,kJAAkJ;AAClJ,iGAAiG;;;;+BACpFA,0BAAAA;;;eAAAA;;;AAAN,MAAMA,yBACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1837, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// Only the main Googlebot search crawler executes JavaScript, not other Google crawlers.\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\n// This regex specifically matches \"Googlebot\" but NOT \"Mediapartners-Google\", \"AdsBot-Google\", etc.\nconst HEADLESS_BROWSER_BOT_UA_RE = /Googlebot(?!-)|Googlebot$/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "isBot", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "undefined"], "mappings": ";;;;;;;;;;;;;;;;IAUSA,sBAAsB,EAAA;eAAtBA,UAAAA,sBAAsB;;IAFlBC,6BAA6B,EAAA;eAA7BA;;IAgBGC,UAAU,EAAA;eAAVA;;IAJAC,KAAK,EAAA;eAALA;;;0BApBuB;AAEvC,mEAAmE;AACnE,yFAAyF;AACzF,4FAA4F;AAC5F,oGAAoG;AACpG,MAAMC,6BAA6B;AAE5B,MAAMH,gCAAgCD,UAAAA,sBAAsB,CAACK,MAAM;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOH,2BAA2BI,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,OAAOP,UAAAA,sBAAsB,CAACQ,IAAI,CAACD;AACrC;AAEO,SAASJ,MAAMI,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASL,WAAWK,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOG;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1952, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/path-has-prefix.ts"], "sourcesContent": ["import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n"], "names": ["pathHasPrefix", "path", "prefix", "pathname", "parsePath", "startsWith"], "mappings": ";;;+BASgBA,iBAAAA;;;eAAAA;;;2BATU;AASnB,SAASA,cAAcC,IAAY,EAAEC,MAAc;IACxD,IAAI,OAAOD,SAAS,UAAU;QAC5B,OAAO;IACT;IAEA,MAAM,EAAEE,QAAQ,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACH;IAC/B,OAAOE,aAAaD,UAAUC,SAASE,UAAU,CAACH,SAAS;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1973, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/format-webpack-messages.ts"], "sourcesContent": ["/**\nMIT License\n\nCopyright (c) 2015-present, Facebook, Inc.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n// This file is based on https://github.com/facebook/create-react-app/blob/7b1a32be6ec9f99a6c9a3c66813f3ac09c4736b9/packages/react-dev-utils/formatWebpackMessages.js\n// It's been edited to remove chalk and CRA-specific logic\n\nconst friendlySyntaxErrorLabel = 'Syntax error:'\n\nconst WEBPACK_BREAKING_CHANGE_POLYFILLS =\n  '\\n\\nBREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.'\n\nfunction isLikelyASyntaxError(message: string) {\n  return stripAnsi(message).includes(friendlySyntaxErrorLabel)\n}\n\nlet hadMissingSassError = false\n\n// Cleans up webpack error messages.\nfunction formatMessage(\n  message: any,\n  verbose?: boolean,\n  importTraceNote?: boolean\n) {\n  // TODO: Replace this once webpack 5 is stable\n  if (typeof message === 'object' && message.message) {\n    const filteredModuleTrace =\n      message.moduleTrace &&\n      message.moduleTrace.filter(\n        (trace: any) =>\n          !/next-(middleware|client-pages|route|edge-function)-loader\\.js/.test(\n            trace.originName\n          )\n      )\n\n    let body = message.message\n    const breakingChangeIndex = body.indexOf(WEBPACK_BREAKING_CHANGE_POLYFILLS)\n    if (breakingChangeIndex >= 0) {\n      body = body.slice(0, breakingChangeIndex)\n    }\n\n    message =\n      (message.moduleName ? stripAnsi(message.moduleName) + '\\n' : '') +\n      (message.file ? stripAnsi(message.file) + '\\n' : '') +\n      body +\n      (message.details && verbose ? '\\n' + message.details : '') +\n      (filteredModuleTrace && filteredModuleTrace.length\n        ? (importTraceNote || '\\n\\nImport trace for requested module:') +\n          filteredModuleTrace\n            .map((trace: any) => `\\n${trace.moduleName}`)\n            .join('')\n        : '') +\n      (message.stack && verbose ? '\\n' + message.stack : '')\n  }\n  let lines = message.split('\\n')\n\n  // Strip Webpack-added headers off errors/warnings\n  // https://github.com/webpack/webpack/blob/master/lib/ModuleError.js\n  lines = lines.filter((line: string) => !/Module [A-z ]+\\(from/.test(line))\n\n  // Transform parsing error into syntax error\n  // TODO: move this to our ESLint formatter?\n  lines = lines.map((line: string) => {\n    const parsingError = /Line (\\d+):(?:(\\d+):)?\\s*Parsing error: (.+)$/.exec(\n      line\n    )\n    if (!parsingError) {\n      return line\n    }\n    const [, errorLine, errorColumn, errorMessage] = parsingError\n    return `${friendlySyntaxErrorLabel} ${errorMessage} (${errorLine}:${errorColumn})`\n  })\n\n  message = lines.join('\\n')\n  // Smoosh syntax errors (commonly found in CSS)\n  message = message.replace(\n    /SyntaxError\\s+\\((\\d+):(\\d+)\\)\\s*(.+?)\\n/g,\n    `${friendlySyntaxErrorLabel} $3 ($1:$2)\\n`\n  )\n  // Clean up export errors\n  message = message.replace(\n    /^.*export '(.+?)' was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$2'.`\n  )\n  message = message.replace(\n    /^.*export 'default' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$2' does not contain a default export (imported as '$1').`\n  )\n  message = message.replace(\n    /^.*export '(.+?)' \\(imported as '(.+?)'\\) was not found in '(.+?)'.*$/gm,\n    `Attempted import error: '$1' is not exported from '$3' (imported as '$2').`\n  )\n  lines = message.split('\\n')\n\n  // Remove leading newline\n  if (lines.length > 2 && lines[1].trim() === '') {\n    lines.splice(1, 1)\n  }\n\n  // Cleans up verbose \"module not found\" messages for files and packages.\n  if (lines[1] && lines[1].startsWith('Module not found: ')) {\n    lines = [\n      lines[0],\n      lines[1]\n        .replace('Error: ', '')\n        .replace('Module not found: Cannot find file:', 'Cannot find file:'),\n      ...lines.slice(2),\n    ]\n  }\n\n  // Add helpful message for users trying to use Sass for the first time\n  if (lines[1] && lines[1].match(/Cannot find module.+sass/)) {\n    // ./file.module.scss (<<loader info>>) => ./file.module.scss\n    const firstLine = lines[0].split('!')\n    lines[0] = firstLine[firstLine.length - 1]\n\n    lines[1] =\n      \"To use Next.js' built-in Sass support, you first need to install `sass`.\\n\"\n    lines[1] += 'Run `npm i sass` or `yarn add sass` inside your workspace.\\n'\n    lines[1] += '\\nLearn more: https://nextjs.org/docs/messages/install-sass'\n\n    // dispose of unhelpful stack trace\n    lines = lines.slice(0, 2)\n    hadMissingSassError = true\n  } else if (\n    hadMissingSassError &&\n    message.match(/(sass-loader|resolve-url-loader: CSS error)/)\n  ) {\n    // dispose of unhelpful stack trace following missing sass module\n    lines = []\n  }\n\n  if (!verbose) {\n    message = lines.join('\\n')\n    // Internal stacks are generally useless so we strip them... with the\n    // exception of stacks containing `webpack:` because they're normally\n    // from user code generated by Webpack. For more information see\n    // https://github.com/facebook/create-react-app/pull/1050\n    message = message.replace(\n      /^\\s*at\\s((?!webpack:).)*:\\d+:\\d+[\\s)]*(\\n|$)/gm,\n      ''\n    ) // at ... ...:x:y\n    message = message.replace(/^\\s*at\\s<anonymous>(\\n|$)/gm, '') // at <anonymous>\n\n    message = message.replace(\n      /File was processed with these loaders:\\n(.+[\\\\/](next[\\\\/]dist[\\\\/].+|@next[\\\\/]react-refresh-utils[\\\\/]loader)\\.js\\n)*You may need an additional loader to handle the result of these loaders.\\n/g,\n      ''\n    )\n\n    lines = message.split('\\n')\n  }\n\n  // Remove duplicated newlines\n  lines = (lines as string[]).filter(\n    (line, index, arr) =>\n      index === 0 || line.trim() !== '' || line.trim() !== arr[index - 1].trim()\n  )\n\n  // Reassemble the message\n  message = lines.join('\\n')\n  return message.trim()\n}\n\nexport default function formatWebpackMessages(json: any, verbose?: boolean) {\n  const formattedErrors = json.errors.map((message: any) => {\n    const isUnknownNextFontError = message.message.includes(\n      'An error occurred in `next/font`.'\n    )\n    return formatMessage(message, isUnknownNextFontError || verbose)\n  })\n  const formattedWarnings = json.warnings.map((message: any) => {\n    return formatMessage(message, verbose)\n  })\n\n  // Reorder errors to put the most relevant ones first.\n  let reactServerComponentsError = -1\n\n  for (let i = 0; i < formattedErrors.length; i++) {\n    const error = formattedErrors[i]\n    if (error.includes('ReactServerComponentsError')) {\n      reactServerComponentsError = i\n      break\n    }\n  }\n\n  // Move the reactServerComponentsError to the top if it exists\n  if (reactServerComponentsError !== -1) {\n    const error = formattedErrors.splice(reactServerComponentsError, 1)\n    formattedErrors.unshift(error[0])\n  }\n\n  const result = {\n    ...json,\n    errors: formattedErrors,\n    warnings: formattedWarnings,\n  }\n  if (!verbose && result.errors.some(isLikelyASyntaxError)) {\n    // If there are any syntax errors, show just them.\n    result.errors = result.errors.filter(isLikelyASyntaxError)\n    result.warnings = []\n  }\n  return result\n}\n"], "names": ["formatWebpackMessages", "friendlySyntaxErrorLabel", "WEBPACK_BREAKING_CHANGE_POLYFILLS", "isLikelyASyntaxError", "message", "stripAnsi", "includes", "hadMissingSassError", "formatMessage", "verbose", "importTraceNote", "filteredModuleTrace", "moduleTrace", "filter", "trace", "test", "originName", "body", "breakingChangeIndex", "indexOf", "slice", "moduleName", "file", "details", "length", "map", "join", "stack", "lines", "split", "line", "parsingError", "exec", "errorLine", "errorColumn", "errorMessage", "replace", "trim", "splice", "startsWith", "match", "firstLine", "index", "arr", "json", "formattedErrors", "errors", "isUnknownNextFontError", "formattedWarnings", "warnings", "reactServerComponentsError", "i", "error", "unshift", "result", "some"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;+BAiKA,WAAA;;;eAAwBA;;;;oEAhKF;AACtB,qKAAqK;AACrK,0DAA0D;AAE1D,MAAMC,2BAA2B;AAEjC,MAAMC,oCACJ;AAEF,SAASC,qBAAqBC,OAAe;IAC3C,OAAOC,CAAAA,GAAAA,WAAAA,OAAS,EAACD,SAASE,QAAQ,CAACL;AACrC;AAEA,IAAIM,sBAAsB;AAE1B,oCAAoC;AACpC,SAASC,cACPJ,OAAY,EACZK,OAAiB,EACjBC,eAAyB;IAEzB,8CAA8C;IAC9C,IAAI,OAAON,YAAY,YAAYA,QAAQA,OAAO,EAAE;QAClD,MAAMO,sBACJP,QAAQQ,WAAW,IACnBR,QAAQQ,WAAW,CAACC,MAAM,CACxB,CAACC,QACC,CAAC,gEAAgEC,IAAI,CACnED,MAAME,UAAU;QAIxB,IAAIC,OAAOb,QAAQA,OAAO;QAC1B,MAAMc,sBAAsBD,KAAKE,OAAO,CAACjB;QACzC,IAAIgB,uBAAuB,GAAG;YAC5BD,OAAOA,KAAKG,KAAK,CAAC,GAAGF;QACvB;QAEAd,UACGA,CAAAA,QAAQiB,UAAU,GAAGhB,CAAAA,GAAAA,WAAAA,OAAS,EAACD,QAAQiB,UAAU,IAAI,OAAO,EAAC,IAC7DjB,CAAAA,QAAQkB,IAAI,GAAGjB,CAAAA,GAAAA,WAAAA,OAAS,EAACD,QAAQkB,IAAI,IAAI,OAAO,EAAC,IAClDL,OACCb,CAAAA,QAAQmB,OAAO,IAAId,UAAU,OAAOL,QAAQmB,OAAO,GAAG,EAAC,IACvDZ,CAAAA,uBAAuBA,oBAAoBa,MAAM,GAC7Cd,CAAAA,mBAAmB,wCAAuC,IAC3DC,oBACGc,GAAG,CAAC,CAACX,QAAgB,OAAIA,MAAMO,UAAU,EACzCK,IAAI,CAAC,MACR,EAAC,IACJtB,CAAAA,QAAQuB,KAAK,IAAIlB,UAAU,OAAOL,QAAQuB,KAAK,GAAG,EAAC;IACxD;IACA,IAAIC,QAAQxB,QAAQyB,KAAK,CAAC;IAE1B,kDAAkD;IAClD,oEAAoE;IACpED,QAAQA,MAAMf,MAAM,CAAC,CAACiB,OAAiB,CAAC,uBAAuBf,IAAI,CAACe;IAEpE,4CAA4C;IAC5C,2CAA2C;IAC3CF,QAAQA,MAAMH,GAAG,CAAC,CAACK;QACjB,MAAMC,eAAe,gDAAgDC,IAAI,CACvEF;QAEF,IAAI,CAACC,cAAc;YACjB,OAAOD;QACT;QACA,MAAM,GAAGG,WAAWC,aAAaC,aAAa,GAAGJ;QACjD,OAAU9B,2BAAyB,MAAGkC,eAAa,OAAIF,YAAU,MAAGC,cAAY;IAClF;IAEA9B,UAAUwB,MAAMF,IAAI,CAAC;IACrB,+CAA+C;IAC/CtB,UAAUA,QAAQgC,OAAO,CACvB,4CACC,KAAEnC,2BAAyB;IAE9B,yBAAyB;IACzBG,UAAUA,QAAQgC,OAAO,CACvB,mDACC;IAEHhC,UAAUA,QAAQgC,OAAO,CACvB,6EACC;IAEHhC,UAAUA,QAAQgC,OAAO,CACvB,2EACC;IAEHR,QAAQxB,QAAQyB,KAAK,CAAC;IAEtB,yBAAyB;IACzB,IAAID,MAAMJ,MAAM,GAAG,KAAKI,KAAK,CAAC,EAAE,CAACS,IAAI,OAAO,IAAI;QAC9CT,MAAMU,MAAM,CAAC,GAAG;IAClB;IAEA,wEAAwE;IACxE,IAAIV,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACW,UAAU,CAAC,uBAAuB;QACzDX,QAAQ;YACNA,KAAK,CAAC,EAAE;YACRA,KAAK,CAAC,EAAE,CACLQ,OAAO,CAAC,WAAW,IACnBA,OAAO,CAAC,uCAAuC;eAC/CR,MAAMR,KAAK,CAAC;SAChB;IACH;IAEA,sEAAsE;IACtE,IAAIQ,KAAK,CAAC,EAAE,IAAIA,KAAK,CAAC,EAAE,CAACY,KAAK,CAAC,6BAA6B;QAC1D,6DAA6D;QAC7D,MAAMC,YAAYb,KAAK,CAAC,EAAE,CAACC,KAAK,CAAC;QACjCD,KAAK,CAAC,EAAE,GAAGa,SAAS,CAACA,UAAUjB,MAAM,GAAG,EAAE;QAE1CI,KAAK,CAAC,EAAE,GACN;QACFA,KAAK,CAAC,EAAE,IAAI;QACZA,KAAK,CAAC,EAAE,IAAI;QAEZ,mCAAmC;QACnCA,QAAQA,MAAMR,KAAK,CAAC,GAAG;QACvBb,sBAAsB;IACxB,OAAO,IACLA,uBACAH,QAAQoC,KAAK,CAAC,gDACd;QACA,iEAAiE;QACjEZ,QAAQ,EAAE;IACZ;IAEA,IAAI,CAACnB,SAAS;QACZL,UAAUwB,MAAMF,IAAI,CAAC;QACrB,qEAAqE;QACrE,qEAAqE;QACrE,gEAAgE;QAChE,yDAAyD;QACzDtB,UAAUA,QAAQgC,OAAO,CACvB,kDACA,IACA,iBAAiB;;QACnBhC,UAAUA,QAAQgC,OAAO,CAAC,+BAA+B,IAAI,iBAAiB;;QAE9EhC,UAAUA,QAAQgC,OAAO,CACvB,sMACA;QAGFR,QAAQxB,QAAQyB,KAAK,CAAC;IACxB;IAEA,6BAA6B;IAC7BD,QAASA,MAAmBf,MAAM,CAChC,CAACiB,MAAMY,OAAOC,MACZD,UAAU,KAAKZ,KAAKO,IAAI,OAAO,MAAMP,KAAKO,IAAI,OAAOM,GAAG,CAACD,QAAQ,EAAE,CAACL,IAAI;IAG5E,yBAAyB;IACzBjC,UAAUwB,MAAMF,IAAI,CAAC;IACrB,OAAOtB,QAAQiC,IAAI;AACrB;AAEe,SAASrC,sBAAsB4C,IAAS,EAAEnC,OAAiB;IACxE,MAAMoC,kBAAkBD,KAAKE,MAAM,CAACrB,GAAG,CAAC,CAACrB;QACvC,MAAM2C,yBAAyB3C,QAAQA,OAAO,CAACE,QAAQ,CACrD;QAEF,OAAOE,cAAcJ,SAAS2C,0BAA0BtC;IAC1D;IACA,MAAMuC,oBAAoBJ,KAAKK,QAAQ,CAACxB,GAAG,CAAC,CAACrB;QAC3C,OAAOI,cAAcJ,SAASK;IAChC;IAEA,sDAAsD;IACtD,IAAIyC,6BAA6B,CAAC;IAElC,IAAK,IAAIC,IAAI,GAAGA,IAAIN,gBAAgBrB,MAAM,EAAE2B,IAAK;QAC/C,MAAMC,QAAQP,eAAe,CAACM,EAAE;QAChC,IAAIC,MAAM9C,QAAQ,CAAC,+BAA+B;YAChD4C,6BAA6BC;YAC7B;QACF;IACF;IAEA,8DAA8D;IAC9D,IAAID,+BAA+B,CAAC,GAAG;QACrC,MAAME,QAAQP,gBAAgBP,MAAM,CAACY,4BAA4B;QACjEL,gBAAgBQ,OAAO,CAACD,KAAK,CAAC,EAAE;IAClC;IAEA,MAAME,SAAS;QACb,GAAGV,IAAI;QACPE,QAAQD;QACRI,UAAUD;IACZ;IACA,IAAI,CAACvC,WAAW6C,OAAOR,MAAM,CAACS,IAAI,CAACpD,uBAAuB;QACxD,kDAAkD;QAClDmD,OAAOR,MAAM,GAAGQ,OAAOR,MAAM,CAACjC,MAAM,CAACV;QACrCmD,OAAOL,QAAQ,GAAG,EAAE;IACtB;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/is-plain-object.ts"], "sourcesContent": ["export function getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nexport function isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n\n  /**\n   * this used to be previously:\n   *\n   * `return prototype === null || prototype === Object.prototype`\n   *\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\n   *\n   * It was changed to the current implementation since it's resilient to serialization.\n   */\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\n}\n"], "names": ["getObjectClassLabel", "isPlainObject", "value", "Object", "prototype", "toString", "call", "getPrototypeOf", "hasOwnProperty"], "mappings": ";;;;;;;;;;;;;;IAAgBA,mBAAmB,EAAA;eAAnBA;;IAIAC,aAAa,EAAA;eAAbA;;;AAJT,SAASD,oBAAoBE,KAAU;IAC5C,OAAOC,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ;AACxC;AAEO,SAASD,cAAcC,KAAU;IACtC,IAAIF,oBAAoBE,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAME,YAAYD,OAAOI,cAAc,CAACL;IAExC;;;;;;;;GAQC,GACD,OAAOE,cAAc,QAAQA,UAAUI,cAAc,CAAC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2175, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/error-source.ts"], "sourcesContent": ["const symbolError = Symbol.for('NextjsError')\n\nexport function getErrorSource(error: Error): 'server' | 'edge-server' | null {\n  return (error as any)[symbolError] || null\n}\n\nexport type ErrorSourceType = 'edge-server' | 'server'\n\nexport function decorateServerError(error: Error, type: ErrorSourceType) {\n  Object.defineProperty(error, symbolError, {\n    writable: false,\n    enumerable: false,\n    configurable: false,\n    value: type,\n  })\n}\n"], "names": ["decorateServerError", "getErrorSource", "symbolError", "Symbol", "for", "error", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value"], "mappings": ";;;;;;;;;;;;;;IAQgBA,mBAAmB,EAAA;eAAnBA;;IANAC,cAAc,EAAA;eAAdA;;;AAFhB,MAAMC,cAAcC,OAAOC,GAAG,CAAC;AAExB,SAASH,eAAeI,KAAY;IACzC,OAAQA,KAAa,CAACH,YAAY,IAAI;AACxC;AAIO,SAASF,oBAAoBK,KAAY,EAAEC,IAAqB;IACrEC,OAAOC,cAAc,CAACH,OAAOH,aAAa;QACxCO,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2212, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/errors/constants.ts"], "sourcesContent": ["export const MISSING_ROOT_TAGS_ERROR = 'NEXT_MISSING_ROOT_TAGS'\n"], "names": ["MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;+BAAaA,2BAAAA;;;eAAAA;;;AAAN,MAAMA,0BAA0B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2233, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/normalized-asset-prefix.ts"], "sourcesContent": ["export function normalizedAssetPrefix(assetPrefix: string | undefined): string {\n  // remove all leading slashes and trailing slashes\n  const escapedAssetPrefix = assetPrefix?.replace(/^\\/+|\\/+$/g, '') || false\n\n  // if an assetPrefix was '/', we return empty string\n  // because it could be an unnecessary trailing slash\n  if (!escapedAssetPrefix) {\n    return ''\n  }\n\n  if (URL.canParse(escapedAssetPrefix)) {\n    const url = new URL(escapedAssetPrefix).toString()\n    return url.endsWith('/') ? url.slice(0, -1) : url\n  }\n\n  // assuming assetPrefix here is a pathname-style,\n  // restore the leading slash\n  return `/${escapedAssetPrefix}`\n}\n"], "names": ["normalizedAssetPrefix", "assetPrefix", "escapedAssetPrefix", "replace", "URL", "canParse", "url", "toString", "endsWith", "slice"], "mappings": ";;;+BAAgBA,yBAAAA;;;eAAAA;;;AAAT,SAASA,sBAAsBC,WAA+B;IACnE,kDAAkD;IAClD,MAAMC,qBAAqBD,CAAAA,eAAAA,OAAAA,KAAAA,IAAAA,YAAaE,OAAO,CAAC,cAAc,GAAA,KAAO;IAErE,oDAAoD;IACpD,oDAAoD;IACpD,IAAI,CAACD,oBAAoB;QACvB,OAAO;IACT;IAEA,IAAIE,IAAIC,QAAQ,CAACH,qBAAqB;QACpC,MAAMI,MAAM,IAAIF,IAAIF,oBAAoBK,QAAQ;QAChD,OAAOD,IAAIE,QAAQ,CAAC,OAAOF,IAAIG,KAAK,CAAC,GAAG,CAAC,KAAKH;IAChD;IAEA,iDAAiD;IACjD,4BAA4B;IAC5B,OAAQ,MAAGJ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2262, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/server-reference-info.ts"], "sourcesContent": ["export interface ServerReferenceInfo {\n  type: 'server-action' | 'use-cache'\n  usedArgs: [boolean, boolean, boolean, boolean, boolean, boolean]\n  hasRestArgs: boolean\n}\n\n/**\n * Extracts info about the server reference for the given server reference ID by\n * parsing the first byte of the hex-encoded ID.\n *\n * ```\n * Bit positions: [7]      [6] [5] [4] [3] [2] [1]  [0]\n * Bits:          typeBit  argMask                  restArgs\n * ```\n *\n * If the `typeBit` is `1` the server reference represents a `\"use cache\"`\n * function, otherwise a server action.\n *\n * The `argMask` encodes whether the function uses the argument at the\n * respective position.\n *\n * The `restArgs` bit indicates whether the function uses a rest parameter. It's\n * also set to 1 if the function has more than 6 args.\n *\n * @param id hex-encoded server reference ID\n */\nexport function extractInfoFromServerReferenceId(\n  id: string\n): ServerReferenceInfo {\n  const infoByte = parseInt(id.slice(0, 2), 16)\n  const typeBit = (infoByte >> 7) & 0x1\n  const argMask = (infoByte >> 1) & 0x3f\n  const restArgs = infoByte & 0x1\n  const usedArgs = Array(6)\n\n  for (let index = 0; index < 6; index++) {\n    const bitPosition = 5 - index\n    const bit = (argMask >> bitPosition) & 0x1\n    usedArgs[index] = bit === 1\n  }\n\n  return {\n    type: typeBit === 1 ? 'use-cache' : 'server-action',\n    usedArgs: usedArgs as [\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n    ],\n    hasRestArgs: restArgs === 1,\n  }\n}\n\n/**\n * Creates a sparse array containing only the used arguments based on the\n * provided action info.\n */\nexport function omitUnusedArgs(\n  args: unknown[],\n  info: ServerReferenceInfo\n): unknown[] {\n  const filteredArgs = new Array(args.length)\n\n  for (let index = 0; index < args.length; index++) {\n    if (\n      (index < 6 && info.usedArgs[index]) ||\n      // This assumes that the server reference info byte has the restArgs bit\n      // set to 1 if there are more than 6 args.\n      (index >= 6 && info.hasRestArgs)\n    ) {\n      filteredArgs[index] = args[index]\n    }\n  }\n\n  return filteredArgs\n}\n"], "names": ["extractInfoFromServerReferenceId", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "infoByte", "parseInt", "slice", "typeBit", "argMask", "restArgs", "usedArgs", "Array", "index", "bitPosition", "bit", "type", "hasRestArgs", "args", "info", "filteredArgs", "length"], "mappings": ";;;;;;;;;;;;;;IA0BgBA,gCAAgC,EAAA;eAAhCA;;IAiCAC,cAAc,EAAA;eAAdA;;;AAjCT,SAASD,iCACdE,EAAU;IAEV,MAAMC,WAAWC,SAASF,GAAGG,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAMC,UAAWH,YAAY,IAAK;IAClC,MAAMI,UAAWJ,YAAY,IAAK;IAClC,MAAMK,WAAWL,WAAW;IAC5B,MAAMM,WAAWC,MAAM;IAEvB,IAAK,IAAIC,QAAQ,GAAGA,QAAQ,GAAGA,QAAS;QACtC,MAAMC,cAAc,IAAID;QACxB,MAAME,MAAON,WAAWK,cAAe;QACvCH,QAAQ,CAACE,MAAM,GAAGE,QAAQ;IAC5B;IAEA,OAAO;QACLC,MAAMR,YAAY,IAAI,cAAc;QACpCG,UAAUA;QAQVM,aAAaP,aAAa;IAC5B;AACF;AAMO,SAASP,eACde,IAAe,EACfC,IAAyB;IAEzB,MAAMC,eAAe,IAAIR,MAAMM,KAAKG,MAAM;IAE1C,IAAK,IAAIR,QAAQ,GAAGA,QAAQK,KAAKG,MAAM,EAAER,QAAS;QAChD,IACGA,QAAQ,KAAKM,KAAKR,QAAQ,CAACE,MAAM,IAClC,wEAAwE;QACxE,0CAA0C;QACzCA,SAAS,KAAKM,KAAKF,WAAW,EAC/B;YACAG,YAAY,CAACP,MAAM,GAAGK,IAAI,CAACL,MAAM;QACnC;IACF;IAEA,OAAOO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2315, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2342, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": ";;;+BA<PERSON><PERSON>,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2365, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/use-app-dev-rendering-indicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useTransition } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\n\nexport const useAppDevRenderingIndicator = () => {\n  const [isPending, startTransition] = useTransition()\n\n  useEffect(() => {\n    if (isPending) {\n      dispatcher.renderingIndicatorShow()\n    } else {\n      dispatcher.renderingIndicatorHide()\n    }\n  }, [isPending])\n\n  return startTransition\n}\n"], "names": ["useAppDevRenderingIndicator", "isPending", "startTransition", "useTransition", "useEffect", "dispatcher", "renderingIndicatorShow", "renderingIndicatorHide"], "mappings": ";;;+BAKaA,+BAAAA;;;eAAAA;;;uBAH4B;8BACd;AAEpB,MAAMA,8BAA8B;IACzC,MAAM,CAACC,WAAWC,gBAAgB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa;IAElDC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,IAAIH,WAAW;YACbI,cAAAA,UAAU,CAACC,sBAAsB;QACnC,OAAO;YACLD,cAAAA,UAAU,CAACE,sBAAsB;QACnC;IACF,GAAG;QAACN;KAAU;IAEd,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/shared/console-error.ts"], "sourcesContent": ["// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\nconst digestSym = Symbol.for('next.console.error.digest')\n\n// Represent non Error shape unhandled promise rejections or console.error errors.\n// Those errors will be captured and displayed in Error Overlay.\ntype ConsoleError = Error & {\n  [digestSym]: 'NEXT_CONSOLE_ERROR'\n  environmentName: string\n}\n\nexport function createConsoleError(\n  message: string | Error,\n  environmentName?: string | null\n): ConsoleError {\n  const error = (\n    typeof message === 'string' ? new Error(message) : message\n  ) as ConsoleError\n  error[digestSym] = 'NEXT_CONSOLE_ERROR'\n\n  if (environmentName && !error.environmentName) {\n    error.environmentName = environmentName\n  }\n\n  return error\n}\n\nexport const isConsoleError = (error: any): error is ConsoleError => {\n  return error && error[digestSym] === 'NEXT_CONSOLE_ERROR'\n}\n"], "names": ["createConsoleError", "isConsoleError", "digestSym", "Symbol", "for", "message", "environmentName", "error", "Error"], "mappings": "AAAA,yJAAyJ;;;;;;;;;;;;;;;IAUzIA,kBAAkB,EAAA;eAAlBA;;IAgBHC,cAAc,EAAA;eAAdA;;;AAzBb,MAAMC,YAAYC,OAAOC,GAAG,CAAC;AAStB,SAASJ,mBACdK,OAAuB,EACvBC,eAA+B;IAE/B,MAAMC,QACJ,OAAOF,YAAY,WAAW,OAAA,cAAkB,CAAlB,IAAIG,MAAMH,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB,KAAIA;IAErDE,KAAK,CAACL,UAAU,GAAG;IAEnB,IAAII,mBAAmB,CAACC,MAAMD,eAAe,EAAE;QAC7CC,MAAMD,eAAe,GAAGA;IAC1B;IAEA,OAAOC;AACT;AAEO,MAAMN,iBAAiB,CAACM;IAC7B,OAAOA,SAASA,KAAK,CAACL,UAAU,KAAK;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/errors/stitched-error.ts"], "sourcesContent": ["import React from 'react'\nimport isError from '../../../../lib/is-error'\n\nconst ownerStacks = new WeakMap<Error, string | null>()\n\nexport function getOwnerStack(error: Error): string | null | undefined {\n  return ownerStacks.get(error)\n}\nexport function setOwnerStack(error: Error, stack: string | null) {\n  ownerStacks.set(error, stack)\n}\n\nexport function coerceError(value: unknown): Error {\n  return isError(value) ? value : new Error('' + value)\n}\n\nexport function setOwnerStackIfAvailable(error: Error): void {\n  // React 18 and prod does not have `captureOwnerStack`\n  if ('captureOwnerStack' in React) {\n    setOwnerStack(error, React.captureOwnerStack())\n  }\n}\n\nexport function decorateDevError(thrownValue: unknown) {\n  const error = coerceError(thrownValue)\n  setOwnerStackIfAvailable(error)\n  return error\n}\n"], "names": ["coerceError", "decorateDevError", "getOwnerStack", "setOwnerStack", "setOwnerStackIfAvailable", "ownerStacks", "WeakMap", "error", "get", "stack", "set", "value", "isError", "Error", "React", "captureOwnerStack", "thrownValue"], "mappings": ";;;;;;;;;;;;;;;;;IAYgBA,WAAW,EAAA;eAAXA;;IAWAC,gBAAgB,EAAA;eAAhBA;;IAlBAC,aAAa,EAAA;eAAbA;;IAGAC,aAAa,EAAA;eAAbA;;IAQAC,wBAAwB,EAAA;eAAxBA;;;;gEAhBE;kEACE;AAEpB,MAAMC,cAAc,IAAIC;AAEjB,SAASJ,cAAcK,KAAY;IACxC,OAAOF,YAAYG,GAAG,CAACD;AACzB;AACO,SAASJ,cAAcI,KAAY,EAAEE,KAAoB;IAC9DJ,YAAYK,GAAG,CAACH,OAAOE;AACzB;AAEO,SAAST,YAAYW,KAAc;IACxC,OAAOC,CAAAA,GAAAA,SAAAA,OAAO,EAACD,SAASA,QAAQ,OAAA,cAAqB,CAArB,IAAIE,MAAM,KAAKF,QAAf,qBAAA;eAAA;oBAAA;sBAAA;IAAoB;AACtD;AAEO,SAASP,yBAAyBG,KAAY;IACnD,sDAAsD;IACtD,IAAI,uBAAuBO,OAAAA,OAAK,EAAE;QAChCX,cAAcI,OAAOO,OAAAA,OAAK,CAACC,iBAAiB;IAC9C;AACF;AAEO,SAASd,iBAAiBe,WAAoB;IACnD,MAAMT,QAAQP,YAAYgB;IAC1BZ,yBAAyBG;IACzB,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2521, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/terminal-logging-config.ts"], "sourcesContent": ["export function getTerminalLoggingConfig():\n  | false\n  | boolean\n  | {\n      depthLimit?: number\n      edgeLimit?: number\n      showSourceLocation?: boolean\n    } {\n  try {\n    return JSON.parse(\n      process.env.__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL || 'false'\n    )\n  } catch {\n    return false\n  }\n}\n\nexport function getIsTerminalLoggingEnabled(): boolean {\n  const config = getTerminalLoggingConfig()\n  return Boolean(config)\n}\n"], "names": ["getIsTerminalLoggingEnabled", "getTerminalLoggingConfig", "JSON", "parse", "process", "env", "__NEXT_BROWSER_DEBUG_INFO_IN_TERMINAL", "config", "Boolean"], "mappings": ";;;;;;;;;;;;;;IAiBgBA,2BAA2B,EAAA;eAA3BA;;IAjBAC,wBAAwB,EAAA;eAAxBA;;;AAAT,SAASA;IAQd,IAAI;QACF,OAAOC,KAAKC,KAAK,CACfC,QAAQC,GAAG,CAACC,iCAAyC,IAAJ;IAErD,EAAE,OAAA,GAAM;QACN,OAAO;IACT;AACF;AAEO,SAASN;IACd,MAAMO,SAASN;IACf,OAAOO,QAAQD;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2564, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/shared/forward-logs-shared.ts"], "sourcesContent": ["export type LogMethod =\n  | 'log'\n  | 'info'\n  | 'debug'\n  | 'table'\n  | 'error'\n  | 'assert'\n  | 'dir'\n  | 'dirxml'\n  | 'group'\n  | 'groupCollapsed'\n  | 'groupEnd'\n  | 'trace'\n  | 'warn'\n\nexport type ConsoleEntry<T> = {\n  kind: 'console'\n  method: LogMethod\n  consoleMethodStack: string | null\n  args: Array<\n    | {\n        kind: 'arg'\n        data: T\n      }\n    | {\n        kind: 'formatted-error-arg'\n        prefix: string\n        stack: string\n      }\n  >\n}\n\nexport type ConsoleErrorEntry<T> = {\n  kind: 'any-logged-error'\n  method: 'error'\n  consoleErrorStack: string\n  args: Array<\n    | {\n        kind: 'arg'\n        data: T\n        isRejectionMessage?: boolean\n      }\n    | {\n        kind: 'formatted-error-arg'\n        prefix: string\n        stack: string | null\n      }\n  >\n}\n\nexport type FormattedErrorEntry = {\n  kind: 'formatted-error'\n  prefix: string\n  stack: string\n  method: 'error'\n}\n\nexport type ClientLogEntry =\n  | ConsoleEntry<unknown>\n  | ConsoleErrorEntry<unknown>\n  | FormattedErrorEntry\nexport type ServerLogEntry =\n  | ConsoleEntry<string>\n  | ConsoleErrorEntry<string>\n  | FormattedErrorEntry\n\nexport const UNDEFINED_MARKER = '__next_tagged_undefined'\n\n// Based on https://github.com/facebook/react/blob/28dc0776be2e1370fe217549d32aee2519f0cf05/packages/react-server/src/ReactFlightServer.js#L248\nexport function patchConsoleMethod<T extends keyof Console>(\n  methodName: T,\n  wrapper: (\n    methodName: T,\n    ...args: Console[T] extends (...args: infer P) => any ? P : never[]\n  ) => void\n): () => void {\n  const descriptor = Object.getOwnPropertyDescriptor(console, methodName)\n  if (\n    descriptor &&\n    (descriptor.configurable || descriptor.writable) &&\n    typeof descriptor.value === 'function'\n  ) {\n    const originalMethod = descriptor.value as Console[T] extends (\n      ...args: any[]\n    ) => any\n      ? Console[T]\n      : never\n    const originalName = Object.getOwnPropertyDescriptor(originalMethod, 'name')\n    const wrapperMethod = function (\n      this: typeof console,\n      ...args: Console[T] extends (...args: infer P) => any ? P : never[]\n    ) {\n      wrapper(methodName, ...args)\n\n      originalMethod.apply(this, args)\n    }\n    if (originalName) {\n      Object.defineProperty(wrapperMethod, 'name', originalName)\n    }\n    Object.defineProperty(console, methodName, {\n      value: wrapperMethod,\n    })\n\n    return () => {\n      Object.defineProperty(console, methodName, {\n        value: originalMethod,\n        writable: descriptor.writable,\n        configurable: descriptor.configurable,\n      })\n    }\n  }\n\n  return () => {}\n}\n"], "names": ["UNDEFINED_MARKER", "patchConsoleMethod", "methodName", "wrapper", "descriptor", "Object", "getOwnPropertyDescriptor", "console", "configurable", "writable", "value", "originalMethod", "originalName", "wrapperMethod", "args", "apply", "defineProperty"], "mappings": ";;;;;;;;;;;;;;IAkEaA,gBAAgB,EAAA;eAAhBA;;IAGGC,kBAAkB,EAAA;eAAlBA;;;AAHT,MAAMD,mBAAmB;AAGzB,SAASC,mBACdC,UAAa,EACbC,OAGS;IAET,MAAMC,aAAaC,OAAOC,wBAAwB,CAACC,SAASL;IAC5D,IACEE,cACCA,CAAAA,WAAWI,YAAY,IAAIJ,WAAWK,QAAO,KAC9C,OAAOL,WAAWM,KAAK,KAAK,YAC5B;QACA,MAAMC,iBAAiBP,WAAWM,KAAK;QAKvC,MAAME,eAAeP,OAAOC,wBAAwB,CAACK,gBAAgB;QACrE,MAAME,gBAAgB;YAEpB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;gBAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAAmE;;YAEnEX,QAAQD,eAAeY;YAEvBH,eAAeI,KAAK,CAAC,IAAI,EAAED;QAC7B;QACA,IAAIF,cAAc;YAChBP,OAAOW,cAAc,CAACH,eAAe,QAAQD;QAC/C;QACAP,OAAOW,cAAc,CAACT,SAASL,YAAY;YACzCQ,OAAOG;QACT;QAEA,OAAO;YACLR,OAAOW,cAAc,CAACT,SAASL,YAAY;gBACzCQ,OAAOC;gBACPF,UAAUL,WAAWK,QAAQ;gBAC7BD,cAAcJ,WAAWI,YAAY;YACvC;QACF;IACF;IAEA,OAAO,KAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2625, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/forward-logs.ts"], "sourcesContent": ["import { configure } from 'next/dist/compiled/safe-stable-stringify'\nimport {\n  getOwnerStack,\n  setOwnerStackIfAvailable,\n} from './errors/stitched-error'\nimport { getErrorSource } from '../../../shared/lib/error-source'\nimport {\n  getTerminalLoggingConfig,\n  getIsTerminalLoggingEnabled,\n} from './terminal-logging-config'\nimport {\n  type ConsoleEntry,\n  type ConsoleErrorEntry,\n  type FormattedErrorEntry,\n  type ClientLogEntry,\n  type LogMethod,\n  patchConsoleMethod,\n  UNDEFINED_MARKER,\n} from '../../shared/forward-logs-shared'\n\nconst terminalLoggingConfig = getTerminalLoggingConfig()\nexport const PROMISE_MARKER = 'Promise {}'\nexport const UNAVAILABLE_MARKER = '[Unable to view]'\n\nconst maximumDepth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.depthLimit\n    ? terminalLoggingConfig.depthLimit\n    : 5\nconst maximumBreadth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.edgeLimit\n    ? terminalLoggingConfig.edgeLimit\n    : 100\n\nconst stringify = configure({\n  maximumDepth,\n  maximumBreadth,\n})\n\nexport const isTerminalLoggingEnabled = getIsTerminalLoggingEnabled()\n\nconst methods: Array<LogMethod> = [\n  'log',\n  'info',\n  'warn',\n  'debug',\n  'table',\n  'assert',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n  'trace',\n]\n/**\n * allows us to:\n * - revive the undefined log in the server as it would look in the browser\n * - not read/attempt to serialize promises (next will console error if you do that, and will cause this program to infinitely recurse)\n * - if we read a proxy that throws (no way to detect if something is a proxy), explain to the user we can't read this data\n */\nexport function preLogSerializationClone<T>(\n  value: T,\n  seen = new WeakMap()\n): any {\n  if (value === undefined) return UNDEFINED_MARKER\n  if (value === null || typeof value !== 'object') return value\n  if (seen.has(value as object)) return seen.get(value as object)\n\n  try {\n    Object.keys(value as object)\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  try {\n    if (typeof (value as any).then === 'function') return PROMISE_MARKER\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  if (Array.isArray(value)) {\n    const out: any[] = []\n    seen.set(value, out)\n    for (const item of value) {\n      try {\n        out.push(preLogSerializationClone(item, seen))\n      } catch {\n        out.push(UNAVAILABLE_MARKER)\n      }\n    }\n    return out\n  }\n\n  const proto = Object.getPrototypeOf(value)\n  if (proto === Object.prototype || proto === null) {\n    const out: Record<string, unknown> = {}\n    seen.set(value as object, out)\n    for (const key of Object.keys(value as object)) {\n      try {\n        out[key] = preLogSerializationClone((value as any)[key], seen)\n      } catch {\n        out[key] = UNAVAILABLE_MARKER\n      }\n    }\n    return out\n  }\n\n  return Object.prototype.toString.call(value)\n}\n\n// only safe if passed safeClone data\nexport const logStringify = (data: unknown): string => {\n  try {\n    const result = stringify(data)\n    return result ?? `\"${UNAVAILABLE_MARKER}\"`\n  } catch {\n    return `\"${UNAVAILABLE_MARKER}\"`\n  }\n}\n\nconst afterThisFrame = (cb: () => void) => {\n  let timeout: ReturnType<typeof setTimeout> | undefined\n\n  const rafId = requestAnimationFrame(() => {\n    timeout = setTimeout(() => {\n      cb()\n    })\n  })\n\n  return () => {\n    cancelAnimationFrame(rafId)\n    clearTimeout(timeout)\n  }\n}\n\nlet isPatched = false\n\nconst serializeEntries = (entries: Array<ClientLogEntry>) =>\n  entries.map((clientEntry) => {\n    switch (clientEntry.kind) {\n      case 'any-logged-error':\n      case 'console': {\n        return {\n          ...clientEntry,\n          args: clientEntry.args.map(stringifyUserArg),\n        }\n      }\n      case 'formatted-error': {\n        return clientEntry\n      }\n      default: {\n        return null!\n      }\n    }\n  })\n\nexport const logQueue: {\n  entries: Array<ClientLogEntry>\n  onSocketReady: (socket: WebSocket) => void\n  flushScheduled: boolean\n  socket: WebSocket | null\n  cancelFlush: (() => void) | null\n  sourceType?: 'server' | 'edge-server'\n  router: 'app' | 'pages' | null\n  scheduleLogSend: (entry: ClientLogEntry) => void\n} = {\n  entries: [],\n  flushScheduled: false,\n  cancelFlush: null,\n  socket: null,\n  sourceType: undefined,\n  router: null,\n  scheduleLogSend: (entry: ClientLogEntry) => {\n    logQueue.entries.push(entry)\n    if (logQueue.flushScheduled) {\n      return\n    }\n    // safe to deref and use in setTimeout closure since we cancel on new socket\n    const socket = logQueue.socket\n    if (!socket) {\n      return\n    }\n\n    // we probably dont need this\n    logQueue.flushScheduled = true\n\n    // non blocking log flush, runs at most once per frame\n    logQueue.cancelFlush = afterThisFrame(() => {\n      logQueue.flushScheduled = false\n\n      // just incase\n      try {\n        const payload = JSON.stringify({\n          event: 'browser-logs',\n          entries: serializeEntries(logQueue.entries),\n          router: logQueue.router,\n          // needed for source mapping, we just assign the sourceType from the last error for the whole batch\n          sourceType: logQueue.sourceType,\n        })\n\n        socket.send(payload)\n        logQueue.entries = []\n        logQueue.sourceType = undefined\n      } catch {\n        // error (make sure u don't infinite loop)\n        /* noop */\n      }\n    })\n  },\n  onSocketReady: (socket: WebSocket) => {\n    if (socket.readyState !== WebSocket.OPEN) {\n      // invariant\n      return\n    }\n\n    // incase an existing timeout was going to run with a stale socket\n    logQueue.cancelFlush?.()\n    logQueue.socket = socket\n    try {\n      const payload = JSON.stringify({\n        event: 'browser-logs',\n        entries: serializeEntries(logQueue.entries),\n        router: logQueue.router,\n        sourceType: logQueue.sourceType,\n      })\n\n      socket.send(payload)\n      logQueue.entries = []\n      logQueue.sourceType = undefined\n    } catch {\n      /** noop just incase */\n    }\n  },\n}\n\nconst stringifyUserArg = (\n  arg:\n    | {\n        kind: 'arg'\n        data: unknown\n      }\n    | {\n        kind: 'formatted-error-arg'\n      }\n) => {\n  if (arg.kind !== 'arg') {\n    return arg\n  }\n  return {\n    ...arg,\n    data: logStringify(arg.data),\n  }\n}\n\nconst createErrorArg = (error: Error) => {\n  const stack = stackWithOwners(error)\n  return {\n    kind: 'formatted-error-arg' as const,\n    prefix: error.message ? `${error.name}: ${error.message}` : `${error.name}`,\n    stack,\n  }\n}\n\nconst createLogEntry = (level: LogMethod, args: any[]) => {\n  // do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n  // error capture stack trace maybe\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n') // this is probably ignored anyways\n  const entry: ConsoleEntry<unknown> = {\n    kind: 'console',\n    consoleMethodStack: cleanStack ?? null, // depending on browser we might not have stack\n    method: level,\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nexport const forwardErrorLog = (args: any[]) => {\n  const errorObjects = args.filter((arg) => arg instanceof Error)\n  const first = errorObjects.at(0)\n  if (first) {\n    const source = getErrorSource(first)\n    if (source) {\n      logQueue.sourceType = source\n    }\n  }\n  /**\n   * browser shows stack regardless of type of data passed to console.error, so we should do the same\n   *\n   * do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n   */\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n')\n\n  const entry: ConsoleErrorEntry<unknown> = {\n    kind: 'any-logged-error',\n    method: 'error',\n    consoleErrorStack: cleanStack ?? '',\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUncaughtErrorEntry = (\n  errorName: string,\n  errorMessage: string,\n  fullStack: string\n) => {\n  const entry: FormattedErrorEntry = {\n    kind: 'formatted-error',\n    prefix: `Uncaught ${errorName}: ${errorMessage}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst stackWithOwners = (error: Error) => {\n  let ownerStack = ''\n  setOwnerStackIfAvailable(error)\n  ownerStack = getOwnerStack(error) || ''\n  const stack = (error.stack || '') + ownerStack\n  return stack\n}\n\nexport function logUnhandledRejection(reason: unknown) {\n  if (reason instanceof Error) {\n    createUnhandledRejectionErrorEntry(reason, stackWithOwners(reason))\n    return\n  }\n  createUnhandledRejectionNonErrorEntry(reason)\n}\n\nconst createUnhandledRejectionErrorEntry = (\n  error: Error,\n  fullStack: string\n) => {\n  const source = getErrorSource(error)\n  if (source) {\n    logQueue.sourceType = source\n  }\n\n  const entry: ClientLogEntry = {\n    kind: 'formatted-error',\n    prefix: `⨯ unhandledRejection: ${error.name}: ${error.message}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUnhandledRejectionNonErrorEntry = (reason: unknown) => {\n  const entry: ClientLogEntry = {\n    kind: 'any-logged-error',\n    // we can't access the stack since the event is dispatched async and creating an inline error would be meaningless\n    consoleErrorStack: '',\n    method: 'error',\n    args: [\n      {\n        kind: 'arg',\n        data: `⨯ unhandledRejection:`,\n        isRejectionMessage: true,\n      },\n      {\n        kind: 'arg',\n        data: preLogSerializationClone(reason),\n      },\n    ],\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst isHMR = (args: any[]) => {\n  const firstArg = args[0]\n  if (typeof firstArg !== 'string') {\n    return false\n  }\n  if (firstArg.startsWith('[Fast Refresh]')) {\n    return true\n  }\n\n  if (firstArg.startsWith('[HMR]')) {\n    return true\n  }\n\n  return false\n}\n\nconst isIgnoredLog = (args: any[]) => {\n  if (args.length < 3) {\n    return false\n  }\n\n  const [format, styles, label] = args\n\n  if (\n    typeof format !== 'string' ||\n    typeof styles !== 'string' ||\n    typeof label !== 'string'\n  ) {\n    return false\n  }\n\n  // kinda hacky, we should define a common format for these strings so we can safely ignore\n  return format.startsWith('%c%s%c') && styles.includes('background:')\n}\n\nexport function forwardUnhandledError(error: Error) {\n  createUncaughtErrorEntry(error.name, error.message, stackWithOwners(error))\n}\n\n// TODO: this router check is brittle, we need to update based on the current router the user is using\nexport const initializeDebugLogForwarding = (router: 'app' | 'pages'): void => {\n  // probably don't need this\n  if (isPatched) {\n    return\n  }\n  // TODO(rob): why does this break rendering on server, important to know incase the same bug appears in browser\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  // better to be safe than sorry\n  try {\n    methods.forEach((method) =>\n      patchConsoleMethod(method, (_, ...args) => {\n        if (isHMR(args)) {\n          return\n        }\n        if (isIgnoredLog(args)) {\n          return\n        }\n        createLogEntry(method, args)\n      })\n    )\n  } catch {}\n  logQueue.router = router\n  isPatched = true\n}\n"], "names": ["PROMISE_MARKER", "UNAVAILABLE_MARKER", "forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardUnhandledError", "initializeDebugLogForwarding", "isTerminalLoggingEnabled", "logQueue", "logStringify", "logUnhandledRejection", "preLogSerializationClone", "terminalLoggingConfig", "getTerminalLoggingConfig", "maximumDepth", "depthLimit", "maximumBreadth", "edgeLimit", "stringify", "configure", "getIsTerminalLoggingEnabled", "methods", "value", "seen", "WeakMap", "undefined", "UNDEFINED_MARKER", "has", "get", "Object", "keys", "then", "Array", "isArray", "out", "set", "item", "push", "proto", "getPrototypeOf", "prototype", "key", "toString", "call", "data", "result", "afterT<PERSON><PERSON><PERSON>e", "cb", "timeout", "rafId", "requestAnimationFrame", "setTimeout", "cancelAnimationFrame", "clearTimeout", "isPatched", "serializeEntries", "entries", "map", "clientEntry", "kind", "args", "stringifyUserArg", "flushScheduled", "cancelFlush", "socket", "sourceType", "router", "scheduleLogSend", "entry", "payload", "JSON", "event", "send", "onSocketReady", "readyState", "WebSocket", "OPEN", "arg", "createErrorArg", "error", "stack", "stackWithOwners", "prefix", "message", "name", "createLogEntry", "level", "Error", "stackLines", "split", "cleanStack", "slice", "join", "consoleMethodStack", "method", "errorObjects", "filter", "first", "at", "source", "getErrorSource", "consoleErrorStack", "createUncaughtErrorEntry", "errorName", "errorMessage", "fullStack", "ownerStack", "setOwnerStackIfAvailable", "getOwnerStack", "reason", "createUnhandledRejectionErrorEntry", "createUnhandledRejectionNonErrorEntry", "isRejectionMessage", "isHMR", "firstArg", "startsWith", "isIgnoredLog", "length", "format", "styles", "label", "includes", "window", "for<PERSON>ach", "patchConsoleMethod", "_"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAqBaA,cAAc,EAAA;eAAdA;;IACAC,kBAAkB,EAAA;eAAlBA;;IAyQAC,eAAe,EAAA;eAAfA;;IA+IGC,qBAAqB,EAAA;eAArBA;;IAKHC,4BAA4B,EAAA;eAA5BA;;IA7YAC,wBAAwB,EAAA;eAAxBA;;IAsHAC,QAAQ,EAAA;eAARA;;IA7CAC,YAAY,EAAA;eAAZA;;IA2OGC,qBAAqB,EAAA;eAArBA;;IA9RAC,wBAAwB,EAAA;eAAxBA;;;qCA5DU;+BAInB;6BACwB;uCAIxB;mCASA;AAEP,MAAMC,wBAAwBC,CAAAA,GAAAA,uBAAAA,wBAAwB;AAC/C,MAAMX,iBAAiB;AACvB,MAAMC,qBAAqB;AAElC,MAAMW,eACJ,OAAOF,0BAA0B,YAAYA,sBAAsBG,UAAU,GACzEH,sBAAsBG,UAAU,GAChC;AACN,MAAMC,iBACJ,OAAOJ,0BAA0B,YAAYA,sBAAsBK,SAAS,GACxEL,sBAAsBK,SAAS,GAC/B;AAEN,MAAMC,YAAYC,CAAAA,GAAAA,qBAAAA,SAAS,EAAC;IAC1BL;IACAE;AACF;AAEO,MAAMT,2BAA2Ba,CAAAA,GAAAA,uBAAAA,2BAA2B;AAEnE,MAAMC,UAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAOM,SAASV,yBACdW,KAAQ,EACRC,IAAoB;IAApBA,IAAAA,SAAAA,KAAAA,GAAAA,OAAO,IAAIC;IAEX,IAAIF,UAAUG,WAAW,OAAOC,mBAAAA,gBAAgB;IAChD,IAAIJ,UAAU,QAAQ,OAAOA,UAAU,UAAU,OAAOA;IACxD,IAAIC,KAAKI,GAAG,CAACL,QAAkB,OAAOC,KAAKK,GAAG,CAACN;IAE/C,IAAI;QACFO,OAAOC,IAAI,CAACR;IACd,EAAE,OAAA,GAAM;QACN,OAAOnB;IACT;IAEA,IAAI;QACF,IAAI,OAAQmB,MAAcS,IAAI,KAAK,YAAY,OAAO7B;IACxD,EAAE,OAAA,GAAM;QACN,OAAOC;IACT;IAEA,IAAI6B,MAAMC,OAAO,CAACX,QAAQ;QACxB,MAAMY,MAAa,EAAE;QACrBX,KAAKY,GAAG,CAACb,OAAOY;QAChB,KAAK,MAAME,QAAQd,MAAO;YACxB,IAAI;gBACFY,IAAIG,IAAI,CAAC1B,yBAAyByB,MAAMb;YAC1C,EAAE,OAAA,GAAM;gBACNW,IAAIG,IAAI,CAAClC;YACX;QACF;QACA,OAAO+B;IACT;IAEA,MAAMI,QAAQT,OAAOU,cAAc,CAACjB;IACpC,IAAIgB,UAAUT,OAAOW,SAAS,IAAIF,UAAU,MAAM;QAChD,MAAMJ,MAA+B,CAAC;QACtCX,KAAKY,GAAG,CAACb,OAAiBY;QAC1B,KAAK,MAAMO,OAAOZ,OAAOC,IAAI,CAACR,OAAkB;YAC9C,IAAI;gBACFY,GAAG,CAACO,IAAI,GAAG9B,yBAA0BW,KAAa,CAACmB,IAAI,EAAElB;YAC3D,EAAE,OAAA,GAAM;gBACNW,GAAG,CAACO,IAAI,GAAGtC;YACb;QACF;QACA,OAAO+B;IACT;IAEA,OAAOL,OAAOW,SAAS,CAACE,QAAQ,CAACC,IAAI,CAACrB;AACxC;AAGO,MAAMb,eAAe,CAACmC;IAC3B,IAAI;QACF,MAAMC,SAAS3B,UAAU0B;QACzB,OAAOC,UAAAA,OAAAA,SAAW,MAAG1C,qBAAmB;IAC1C,EAAE,OAAA,GAAM;QACN,OAAQ,MAAGA,qBAAmB;IAChC;AACF;AAEA,MAAM2C,iBAAiB,CAACC;IACtB,IAAIC;IAEJ,MAAMC,QAAQC,sBAAsB;QAClCF,UAAUG,WAAW;YACnBJ;QACF;IACF;IAEA,OAAO;QACLK,qBAAqBH;QACrBI,aAAaL;IACf;AACF;AAEA,IAAIM,YAAY;AAEhB,MAAMC,mBAAmB,CAACC,UACxBA,QAAQC,GAAG,CAAC,CAACC;QACX,OAAQA,YAAYC,IAAI;YACtB,KAAK;YACL,KAAK;gBAAW;oBACd,OAAO;wBACL,GAAGD,WAAW;wBACdE,MAAMF,YAAYE,IAAI,CAACH,GAAG,CAACI;oBAC7B;gBACF;YACA,KAAK;gBAAmB;oBACtB,OAAOH;gBACT;YACA;gBAAS;oBACP,OAAO;gBACT;QACF;IACF;AAEK,MAAMlD,WAST;IACFgD,SAAS,EAAE;IACXM,gBAAgB;IAChBC,aAAa;IACbC,QAAQ;IACRC,YAAYxC;IACZyC,QAAQ;IACRC,iBAAiB,CAACC;QAChB5D,SAASgD,OAAO,CAACnB,IAAI,CAAC+B;QACtB,IAAI5D,SAASsD,cAAc,EAAE;YAC3B;QACF;QACA,4EAA4E;QAC5E,MAAME,SAASxD,SAASwD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX;QACF;QAEA,6BAA6B;QAC7BxD,SAASsD,cAAc,GAAG;QAE1B,sDAAsD;QACtDtD,SAASuD,WAAW,GAAGjB,eAAe;YACpCtC,SAASsD,cAAc,GAAG;YAE1B,cAAc;YACd,IAAI;gBACF,MAAMO,UAAUC,KAAKpD,SAAS,CAAC;oBAC7BqD,OAAO;oBACPf,SAASD,iBAAiB/C,SAASgD,OAAO;oBAC1CU,QAAQ1D,SAAS0D,MAAM;oBACvB,mGAAmG;oBACnGD,YAAYzD,SAASyD,UAAU;gBACjC;gBAEAD,OAAOQ,IAAI,CAACH;gBACZ7D,SAASgD,OAAO,GAAG,EAAE;gBACrBhD,SAASyD,UAAU,GAAGxC;YACxB,EAAE,OAAA,GAAM;YACN,0CAA0C;YAC1C,QAAQ,GACV;QACF;IACF;IACAgD,eAAe,CAACT;QACd,IAAIA,OAAOU,UAAU,KAAKC,UAAUC,IAAI,EAAE;YACxC,YAAY;YACZ;QACF;QAEA,kEAAkE;QAClEpE,SAASuD,WAAW,IAAA,OAAA,KAAA,IAApBvD,SAASuD,WAAW,CAAA,IAAA,CAApBvD;QACAA,SAASwD,MAAM,GAAGA;QAClB,IAAI;YACF,MAAMK,UAAUC,KAAKpD,SAAS,CAAC;gBAC7BqD,OAAO;gBACPf,SAASD,iBAAiB/C,SAASgD,OAAO;gBAC1CU,QAAQ1D,SAAS0D,MAAM;gBACvBD,YAAYzD,SAASyD,UAAU;YACjC;YAEAD,OAAOQ,IAAI,CAACH;YACZ7D,SAASgD,OAAO,GAAG,EAAE;YACrBhD,SAASyD,UAAU,GAAGxC;QACxB,EAAE,OAAA,GAAM;QACN,qBAAqB,GACvB;IACF;AACF;AAEA,MAAMoC,mBAAmB,CACvBgB;IASA,IAAIA,IAAIlB,IAAI,KAAK,OAAO;QACtB,OAAOkB;IACT;IACA,OAAO;QACL,GAAGA,GAAG;QACNjC,MAAMnC,aAAaoE,IAAIjC,IAAI;IAC7B;AACF;AAEA,MAAMkC,iBAAiB,CAACC;IACtB,MAAMC,QAAQC,gBAAgBF;IAC9B,OAAO;QACLpB,MAAM;QACNuB,QAAQH,MAAMI,OAAO,GAAMJ,MAAMK,IAAI,GAAC,OAAIL,MAAMI,OAAO,GAAM,KAAEJ,MAAMK,IAAI;QACzEJ;IACF;AACF;AAEA,MAAMK,iBAAiB,CAACC,OAAkB1B;IACxC,0IAA0I;IAC1I,kCAAkC;IAClC,MAAMoB,QAAQC,gBAAgB,IAAIM;IAClC,MAAMC,aAAaR,SAAAA,OAAAA,KAAAA,IAAAA,MAAOS,KAAK,CAAC;IAChC,MAAMC,aAAaF,cAAAA,OAAAA,KAAAA,IAAAA,WAAYG,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,mCAAmC;;IACtF,MAAMxB,QAA+B;QACnCT,MAAM;QACNkC,oBAAoBH,cAAAA,OAAAA,aAAc;QAClCI,QAAQR;QACR1B,MAAMA,KAAKH,GAAG,CAAC,CAACoB;YACd,IAAIA,eAAeU,OAAO;gBACxB,OAAOT,eAAeD;YACxB;YACA,OAAO;gBACLlB,MAAM;gBACNf,MAAMjC,yBAAyBkE;YACjC;QACF;IACF;IAEArE,SAAS2D,eAAe,CAACC;AAC3B;AAEO,MAAMhE,kBAAkB,CAACwD;IAC9B,MAAMmC,eAAenC,KAAKoC,MAAM,CAAC,CAACnB,MAAQA,eAAeU;IACzD,MAAMU,QAAQF,aAAaG,EAAE,CAAC;IAC9B,IAAID,OAAO;QACT,MAAME,SAASC,CAAAA,GAAAA,aAAAA,cAAc,EAACH;QAC9B,IAAIE,QAAQ;YACV3F,SAASyD,UAAU,GAAGkC;QACxB;IACF;IACA;;;;GAIC,GACD,MAAMnB,QAAQC,gBAAgB,IAAIM;IAClC,MAAMC,aAAaR,SAAAA,OAAAA,KAAAA,IAAAA,MAAOS,KAAK,CAAC;IAChC,MAAMC,aAAaF,cAAAA,OAAAA,KAAAA,IAAAA,WAAYG,KAAK,CAAC,GAAGC,IAAI,CAAC;IAE7C,MAAMxB,QAAoC;QACxCT,MAAM;QACNmC,QAAQ;QACRO,mBAAmBX,cAAAA,OAAAA,aAAc;QACjC9B,MAAMA,KAAKH,GAAG,CAAC,CAACoB;YACd,IAAIA,eAAeU,OAAO;gBACxB,OAAOT,eAAeD;YACxB;YACA,OAAO;gBACLlB,MAAM;gBACNf,MAAMjC,yBAAyBkE;YACjC;QACF;IACF;IAEArE,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAMkC,2BAA2B,CAC/BC,WACAC,cACAC;IAEA,MAAMrC,QAA6B;QACjCT,MAAM;QACNuB,QAAS,cAAWqB,YAAU,OAAIC;QAClCxB,OAAOyB;QACPX,QAAQ;IACV;IAEAtF,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAMa,kBAAkB,CAACF;IACvB,IAAI2B,aAAa;IACjBC,CAAAA,GAAAA,eAAAA,wBAAwB,EAAC5B;IACzB2B,aAAaE,CAAAA,GAAAA,eAAAA,aAAa,EAAC7B,UAAU;IACrC,MAAMC,QAASD,CAAAA,MAAMC,KAAK,IAAI,EAAC,IAAK0B;IACpC,OAAO1B;AACT;AAEO,SAAStE,sBAAsBmG,MAAe;IACnD,IAAIA,kBAAkBtB,OAAO;QAC3BuB,mCAAmCD,QAAQ5B,gBAAgB4B;QAC3D;IACF;IACAE,sCAAsCF;AACxC;AAEA,MAAMC,qCAAqC,CACzC/B,OACA0B;IAEA,MAAMN,SAASC,CAAAA,GAAAA,aAAAA,cAAc,EAACrB;IAC9B,IAAIoB,QAAQ;QACV3F,SAASyD,UAAU,GAAGkC;IACxB;IAEA,MAAM/B,QAAwB;QAC5BT,MAAM;QACNuB,QAAS,2BAAwBH,MAAMK,IAAI,GAAC,OAAIL,MAAMI,OAAO;QAC7DH,OAAOyB;QACPX,QAAQ;IACV;IAEAtF,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAM2C,wCAAwC,CAACF;IAC7C,MAAMzC,QAAwB;QAC5BT,MAAM;QACN,kHAAkH;QAClH0C,mBAAmB;QACnBP,QAAQ;QACRlC,MAAM;YACJ;gBACED,MAAM;gBACNf,MAAO;gBACPoE,oBAAoB;YACtB;YACA;gBACErD,MAAM;gBACNf,MAAMjC,yBAAyBkG;YACjC;SACD;IACH;IAEArG,SAAS2D,eAAe,CAACC;AAC3B;AAEA,MAAM6C,QAAQ,CAACrD;IACb,MAAMsD,WAAWtD,IAAI,CAAC,EAAE;IACxB,IAAI,OAAOsD,aAAa,UAAU;QAChC,OAAO;IACT;IACA,IAAIA,SAASC,UAAU,CAAC,mBAAmB;QACzC,OAAO;IACT;IAEA,IAAID,SAASC,UAAU,CAAC,UAAU;QAChC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAMC,eAAe,CAACxD;IACpB,IAAIA,KAAKyD,MAAM,GAAG,GAAG;QACnB,OAAO;IACT;IAEA,MAAM,CAACC,QAAQC,QAAQC,MAAM,GAAG5D;IAEhC,IACE,OAAO0D,WAAW,YAClB,OAAOC,WAAW,YAClB,OAAOC,UAAU,UACjB;QACA,OAAO;IACT;IAEA,0FAA0F;IAC1F,OAAOF,OAAOH,UAAU,CAAC,aAAaI,OAAOE,QAAQ,CAAC;AACxD;AAEO,SAASpH,sBAAsB0E,KAAY;IAChDuB,yBAAyBvB,MAAMK,IAAI,EAAEL,MAAMI,OAAO,EAAEF,gBAAgBF;AACtE;AAGO,MAAMzE,+BAA+B,CAAC4D;IAC3C,2BAA2B;IAC3B,IAAIZ,WAAW;QACb;IACF;IACA,+GAA+G;IAC/G,IAAI,OAAOoE,WAAW,kBAAa;QACjC;IACF;;;AAkBF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3029, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { isNextRouterError } from '../../../../client/components/is-next-router-error'\nimport {\n  formatConsoleArgs,\n  parseConsoleArgs,\n} from '../../../../client/lib/console'\nimport isError from '../../../../lib/is-error'\nimport { createConsoleError } from '../../../shared/console-error'\nimport { coerceError, setOwnerStackIfAvailable } from './stitched-error'\nimport {\n  forwardUnhandledError,\n  isTerminalLoggingEnabled,\n  logUnhandledRejection,\n} from '../forward-logs'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\ntype ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleConsoleError(\n  originError: unknown,\n  consoleErrorArgs: any[]\n) {\n  let error: Error\n  const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n  if (isError(originError)) {\n    error = createConsoleError(originError, environmentName)\n  } else {\n    error = createConsoleError(\n      formatConsoleArgs(consoleErrorArgs),\n      environmentName\n    )\n  }\n  setOwnerStackIfAvailable(error)\n\n  errorQueue.push(error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function handleClientError(error: Error) {\n  errorQueue.push(error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  const thrownValue: unknown = event.error\n  if (isNextRouterError(thrownValue)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (thrownValue) {\n    const error = coerceError(thrownValue)\n    setOwnerStackIfAvailable(error)\n    handleClientError(error)\n    if (isTerminalLoggingEnabled) {\n      forwardUnhandledError(error)\n    }\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason: unknown = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  const error = coerceError(reason)\n  setOwnerStackIfAvailable(error)\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n\n  if (isTerminalLoggingEnabled) {\n    logUnhandledRejection(reason)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["handleClientError", "handleConsoleError", "handleGlobalErrors", "useErrorHandler", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "originError", "consoleErrorArgs", "error", "environmentName", "parseConsoleArgs", "isError", "createConsoleError", "formatConsoleArgs", "setOwnerStackIfAvailable", "push", "handler", "handleOnUnhandledError", "handleOnUnhandledRejection", "useEffect", "for<PERSON>ach", "splice", "indexOf", "length", "onUnhandledError", "event", "thrownValue", "isNextRouterError", "preventDefault", "coerceError", "isTerminalLoggingEnabled", "forwardUnhandledError", "onUnhandledRejection", "ev", "reason", "logUnhandledRejection", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": ";;;;;;;;;;;;;;;;IAmDgBA,iBAAiB,EAAA;eAAjBA;;IA1BAC,kBAAkB,EAAA;eAAlBA;;IAuGAC,kBAAkB,EAAA;eAAlBA;;IAlEAC,eAAe,EAAA;eAAfA;;;;uBA9DU;mCACQ;yBAI3B;kEACa;8BACe;+BACmB;6BAK/C;AAEP,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAE1C,SAASb,mBACdc,WAAoB,EACpBC,gBAAuB;IAEvB,IAAIC;IACJ,MAAM,EAAEC,eAAe,EAAE,GAAGC,CAAAA,GAAAA,SAAAA,gBAAgB,EAACH;IAC7C,IAAII,CAAAA,GAAAA,SAAAA,OAAO,EAACL,cAAc;QACxBE,QAAQI,CAAAA,GAAAA,cAAAA,kBAAkB,EAACN,aAAaG;IAC1C,OAAO;QACLD,QAAQI,CAAAA,GAAAA,cAAAA,kBAAkB,EACxBC,CAAAA,GAAAA,SAAAA,iBAAiB,EAACN,mBAClBE;IAEJ;IACAK,CAAAA,GAAAA,eAAAA,wBAAwB,EAACN;IAEzBN,WAAWa,IAAI,CAACP;IAChB,KAAK,MAAMQ,WAAWb,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbqB,QAAQR;QACV;IACF;AACF;AAEO,SAASjB,kBAAkBiB,KAAY;IAC5CN,WAAWa,IAAI,CAACP;IAChB,KAAK,MAAMQ,WAAWb,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbqB,QAAQR;QACV;IACF;AACF;AAEO,SAASd,gBACduB,sBAAoC,EACpCC,0BAAwC;IAExCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,wBAAwB;QACxBjB,WAAWkB,OAAO,CAACH;QACnBb,eAAegB,OAAO,CAACF;QAEvB,wBAAwB;QACxBf,cAAcY,IAAI,CAACE;QACnBZ,kBAAkBU,IAAI,CAACG;QAEvB,OAAO;YACL,oBAAoB;YACpBf,cAAckB,MAAM,CAAClB,cAAcmB,OAAO,CAACL,yBAAyB;YACpEZ,kBAAkBgB,MAAM,CACtBhB,kBAAkBiB,OAAO,CAACJ,6BAC1B;YAGF,sBAAsB;YACtBhB,WAAWmB,MAAM,CAAC,GAAGnB,WAAWqB,MAAM;YACtCnB,eAAeiB,MAAM,CAAC,GAAGjB,eAAemB,MAAM;QAChD;IACF,GAAG;QAACN;QAAwBC;KAA2B;AACzD;AAEA,SAASM,iBAAiBC,KAA8B;IACtD,MAAMC,cAAuBD,MAAMjB,KAAK;IACxC,IAAImB,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,cAAc;QAClCD,MAAMG,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAIF,aAAa;QACf,MAAMlB,QAAQqB,CAAAA,GAAAA,eAAAA,WAAW,EAACH;QAC1BZ,CAAAA,GAAAA,eAAAA,wBAAwB,EAACN;QACzBjB,kBAAkBiB;QAClB,IAAIsB,aAAAA,wBAAwB,EAAE;YAC5BC,CAAAA,GAAAA,aAAAA,qBAAqB,EAACvB;QACxB;IACF;AACF;AAEA,SAASwB,qBAAqBC,EAAwC;IACpE,MAAMC,SAAkBD,MAAAA,OAAAA,KAAAA,IAAAA,GAAIC,MAAM;IAClC,IAAIP,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACO,SAAS;QAC7BD,GAAGL,cAAc;QACjB;IACF;IAEA,MAAMpB,QAAQqB,CAAAA,GAAAA,eAAAA,WAAW,EAACK;IAC1BpB,CAAAA,GAAAA,eAAAA,wBAAwB,EAACN;IAEzBJ,eAAeW,IAAI,CAACP;IACpB,KAAK,MAAMQ,WAAWX,kBAAmB;QACvCW,QAAQR;IACV;IAEA,IAAIsB,aAAAA,wBAAwB,EAAE;QAC5BK,CAAAA,GAAAA,aAAAA,qBAAqB,EAACD;IACxB;AACF;AAEO,SAASzC;IACd,IAAI,OAAO2C,WAAW,aAAa;;AASrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3168, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/errors/replay-ssr-only-errors.tsx"], "sourcesContent": ["import { useEffect } from 'react'\nimport { handleClientError } from './use-error-handler'\nimport { isNextRouterError } from '../../../../client/components/is-next-router-error'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../../../shared/lib/errors/constants'\n\nfunction readSsrError(): (Error & { digest?: string }) | null {\n  if (typeof document === 'undefined') {\n    return null\n  }\n\n  const ssrErrorTemplateTag = document.querySelector(\n    'template[data-next-error-message]'\n  )\n  if (ssrErrorTemplateTag) {\n    const message: string = ssrErrorTemplateTag.getAttribute(\n      'data-next-error-message'\n    )!\n    const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack')\n    const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest')\n    const error = new Error(message)\n    if (digest) {\n      ;(error as any).digest = digest\n    }\n    // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n    if (isNextRouterError(error)) {\n      return null\n    }\n    error.stack = stack || ''\n    return error\n  }\n\n  return null\n}\n\n/**\n * Needs to be in the same error boundary as the shell.\n * If it commits, we know we recovered from an SSR error.\n * If it doesn't commit, we errored again and React will take care of error reporting.\n */\nexport function ReplaySsrOnlyErrors({\n  onBlockingError,\n}: {\n  onBlockingError: () => void\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to read during render. The attributes will be gone after commit.\n    const ssrError = readSsrError()\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      if (ssrError !== null) {\n        // TODO(veil): Include original Owner Stack (NDX-905)\n        // TODO(veil): Mark as recoverable error\n        // TODO(veil): console.error\n        handleClientError(ssrError)\n\n        // If it's missing root tags, we can't recover, make it blocking.\n        if (ssrError.digest === MISSING_ROOT_TAGS_ERROR) {\n          onBlockingError()\n        }\n      }\n    }, [ssrError, onBlockingError])\n  }\n\n  return null\n}\n"], "names": ["ReplaySsrOnlyErrors", "readSsrError", "document", "ssrErrorTemplateTag", "querySelector", "message", "getAttribute", "stack", "digest", "error", "Error", "isNextRouterError", "onBlockingError", "process", "env", "NODE_ENV", "ssrError", "useEffect", "handleClientError", "MISSING_ROOT_TAGS_ERROR"], "mappings": ";;;+BAuCg<PERSON>,uBAAAA;;;eAAAA;;;uBAvCU;iCACQ;mCACA;2BACM;AAExC,SAASC;IACP,IAAI,OAAOC,aAAa,aAAa;QACnC,OAAO;IACT;IAEA,MAAMC,sBAAsBD,SAASE,aAAa,CAChD;IAEF,IAAID,qBAAqB;QACvB,MAAME,UAAkBF,oBAAoBG,YAAY,CACtD;QAEF,MAAMC,QAAQJ,oBAAoBG,YAAY,CAAC;QAC/C,MAAME,SAASL,oBAAoBG,YAAY,CAAC;QAChD,MAAMG,QAAQ,OAAA,cAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;QAC/B,IAAIG,QAAQ;;YACRC,MAAcD,MAAM,GAAGA;QAC3B;QACA,yFAAyF;QACzF,IAAIG,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACF,QAAQ;YAC5B,OAAO;QACT;QACAA,MAAMF,KAAK,GAAGA,SAAS;QACvB,OAAOE;IACT;IAEA,OAAO;AACT;AAOO,SAAST,oBAAoB,KAInC;IAJmC,IAAA,EAClCY,eAAe,EAGhB,GAJmC;IAKlC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,wEAAwE;QACxE,MAAMC,WAAWf;QACjB,sDAAsD;QACtDgB,CAAAA,GAAAA,OAAAA,SAAS,EAAC;YACR,IAAID,aAAa,MAAM;gBACrB,qDAAqD;gBACrD,wCAAwC;gBACxC,4BAA4B;gBAC5BE,CAAAA,GAAAA,iBAAAA,iBAAiB,EAACF;gBAElB,iEAAiE;gBACjE,IAAIA,SAASR,MAAM,KAAKW,WAAAA,uBAAuB,EAAE;oBAC/CP;gBACF;YACF;QACF,GAAG;YAACI;YAAUJ;SAAgB;IAChC;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3243, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/segment-explorer-node.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  useState,\n  createContext,\n  useContext,\n  use,\n  useMemo,\n  useCallback,\n} from 'react'\nimport { useLayoutEffect } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { notFound } from '../../../client/components/not-found'\n\nexport type SegmentBoundaryType =\n  | 'not-found'\n  | 'error'\n  | 'loading'\n  | 'global-error'\n\nexport const SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE =\n  'NEXT_DEVTOOLS_SIMULATED_ERROR'\n\nexport type SegmentNodeState = {\n  type: string\n  pagePath: string\n  boundaryType: string | null\n  setBoundaryType: (type: SegmentBoundaryType | null) => void\n}\n\nfunction SegmentTrieNode({\n  type,\n  pagePath,\n}: {\n  type: string\n  pagePath: string\n}): React.ReactNode {\n  const { boundaryType, setBoundaryType } = useSegmentState()\n  const nodeState: SegmentNodeState = useMemo(() => {\n    return {\n      type,\n      pagePath,\n      boundaryType,\n      setBoundaryType,\n    }\n  }, [type, pagePath, boundaryType, setBoundaryType])\n\n  // Use `useLayoutEffect` to ensure the state is updated during suspense.\n  // `useEffect` won't work as the state is preserved during suspense.\n  useLayoutEffect(() => {\n    dispatcher.segmentExplorerNodeAdd(nodeState)\n    return () => {\n      dispatcher.segmentExplorerNodeRemove(nodeState)\n    }\n  }, [nodeState])\n\n  return null\n}\n\nfunction NotFoundSegmentNode(): React.ReactNode {\n  notFound()\n}\n\nfunction ErrorSegmentNode(): React.ReactNode {\n  throw new Error(SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE)\n}\n\nconst forever = new Promise(() => {})\nfunction LoadingSegmentNode(): React.ReactNode {\n  use(forever)\n  return null\n}\n\nexport function SegmentViewStateNode({ page }: { page: string }) {\n  useLayoutEffect(() => {\n    dispatcher.segmentExplorerUpdateRouteState(page)\n    return () => {\n      dispatcher.segmentExplorerUpdateRouteState('')\n    }\n  }, [page])\n  return null\n}\n\nexport function SegmentBoundaryTriggerNode() {\n  const { boundaryType } = useSegmentState()\n  let segmentNode: React.ReactNode = null\n  if (boundaryType === 'loading') {\n    segmentNode = <LoadingSegmentNode />\n  } else if (boundaryType === 'not-found') {\n    segmentNode = <NotFoundSegmentNode />\n  } else if (boundaryType === 'error') {\n    segmentNode = <ErrorSegmentNode />\n  }\n  return segmentNode\n}\n\nexport function SegmentViewNode({\n  type,\n  pagePath,\n  children,\n}: {\n  type: string\n  pagePath: string\n  children?: ReactNode\n}): React.ReactNode {\n  const segmentNode = (\n    <SegmentTrieNode key={type} type={type} pagePath={pagePath} />\n  )\n\n  return (\n    <>\n      {segmentNode}\n      {children}\n    </>\n  )\n}\n\nconst SegmentStateContext = createContext<{\n  boundaryType: SegmentBoundaryType | null\n  setBoundaryType: (type: SegmentBoundaryType | null) => void\n}>({\n  boundaryType: null,\n  setBoundaryType: () => {},\n})\n\nexport function SegmentStateProvider({ children }: { children: ReactNode }) {\n  const [boundaryType, setBoundaryType] = useState<SegmentBoundaryType | null>(\n    null\n  )\n\n  const [errorBoundaryKey, setErrorBoundaryKey] = useState(0)\n  const reloadBoundary = useCallback(\n    () => setErrorBoundaryKey((prev) => prev + 1),\n    []\n  )\n\n  const setBoundaryTypeAndReload = useCallback(\n    (type: SegmentBoundaryType | null) => {\n      if (type === null) {\n        reloadBoundary()\n      }\n      setBoundaryType(type)\n    },\n    [reloadBoundary]\n  )\n\n  return (\n    <SegmentStateContext.Provider\n      key={errorBoundaryKey}\n      value={{\n        boundaryType,\n        setBoundaryType: setBoundaryTypeAndReload,\n      }}\n    >\n      {children}\n    </SegmentStateContext.Provider>\n  )\n}\n\nexport function useSegmentState() {\n  return useContext(SegmentStateContext)\n}\n"], "names": ["SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE", "SegmentBoundaryTriggerNode", "SegmentStateProvider", "SegmentViewNode", "SegmentViewStateNode", "useSegmentState", "SegmentTrieNode", "type", "pagePath", "boundaryType", "setBoundaryType", "nodeState", "useMemo", "useLayoutEffect", "dispatcher", "segmentExplorerNodeAdd", "segmentExplorerNodeRemove", "NotFoundSegmentNode", "notFound", "ErrorSegmentNode", "Error", "forever", "Promise", "LoadingSegmentNode", "use", "page", "segmentExplorerUpdateRouteState", "segmentNode", "children", "SegmentStateContext", "createContext", "useState", "error<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "setError<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reloadBoundary", "useCallback", "prev", "setBoundaryTypeAndReload", "Provider", "value", "useContext"], "mappings": ";;;;;;;;;;;;;;;;;;IAqBaA,wCAAwC,EAAA;eAAxCA;;IA+DGC,0BAA0B,EAAA;eAA1BA;;IA0CAC,oBAAoB,EAAA;eAApBA;;IA7BAC,eAAe,EAAA;eAAfA;;IAvBAC,oBAAoB,EAAA;eAApBA;;IAsFAC,eAAe,EAAA;eAAfA;;;;uBAtJT;8BAEoB;0BACF;AAQlB,MAAML,2CACX;AASF,SAASM,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,IAAI,EACJC,QAAQ,EAIT,GANwB;IAOvB,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAGL;IAC1C,MAAMM,YAA8BC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QAC1C,OAAO;YACLL;YACAC;YACAC;YACAC;QACF;IACF,GAAG;QAACH;QAAMC;QAAUC;QAAcC;KAAgB;IAElD,wEAAwE;IACxE,oEAAoE;IACpEG,CAAAA,GAAAA,OAAAA,eAAe,EAAC;QACdC,cAAAA,UAAU,CAACC,sBAAsB,CAACJ;QAClC,OAAO;YACLG,cAAAA,UAAU,CAACE,yBAAyB,CAACL;QACvC;IACF,GAAG;QAACA;KAAU;IAEd,OAAO;AACT;AAEA,SAASM;IACPC,CAAAA,GAAAA,UAAAA,QAAQ;AACV;AAEA,SAASC;IACP,MAAM,OAAA,cAAmD,CAAnD,IAAIC,MAAMpB,2CAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAkD;AAC1D;AAEA,MAAMqB,UAAU,IAAIC,QAAQ,KAAO;AACnC,SAASC;IACPC,CAAAA,GAAAA,OAAAA,GAAG,EAACH;IACJ,OAAO;AACT;AAEO,SAASjB,qBAAqB,KAA0B;IAA1B,IAAA,EAAEqB,IAAI,EAAoB,GAA1B;IACnCZ,CAAAA,GAAAA,OAAAA,eAAe,EAAC;QACdC,cAAAA,UAAU,CAACY,+BAA+B,CAACD;QAC3C,OAAO;YACLX,cAAAA,UAAU,CAACY,+BAA+B,CAAC;QAC7C;IACF,GAAG;QAACD;KAAK;IACT,OAAO;AACT;AAEO,SAASxB;IACd,MAAM,EAAEQ,YAAY,EAAE,GAAGJ;IACzB,IAAIsB,cAA+B;IACnC,IAAIlB,iBAAiB,WAAW;QAC9BkB,cAAAA,WAAAA,GAAc,CAAA,GAAA,YAAA,GAAA,EAACJ,oBAAAA,CAAAA;IACjB,OAAO,IAAId,iBAAiB,aAAa;QACvCkB,cAAAA,WAAAA,GAAc,CAAA,GAAA,YAAA,GAAA,EAACV,qBAAAA,CAAAA;IACjB,OAAO,IAAIR,iBAAiB,SAAS;QACnCkB,cAAAA,WAAAA,GAAc,CAAA,GAAA,YAAA,GAAA,EAACR,kBAAAA,CAAAA;IACjB;IACA,OAAOQ;AACT;AAEO,SAASxB,gBAAgB,KAQ/B;IAR+B,IAAA,EAC9BI,IAAI,EACJC,QAAQ,EACRoB,QAAQ,EAKT,GAR+B;IAS9B,MAAMD,cAAAA,WAAAA,GACJ,CAAA,GAAA,YAAA,GAAA,EAACrB,iBAAAA;QAA2BC,MAAMA;QAAMC,UAAUA;OAA5BD;IAGxB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;YACGoB;YACAC;;;AAGP;AAEA,MAAMC,sBAAAA,WAAAA,GAAsBC,CAAAA,GAAAA,OAAAA,aAAa,EAGtC;IACDrB,cAAc;IACdC,iBAAiB,KAAO;AAC1B;AAEO,SAASR,qBAAqB,KAAqC;IAArC,IAAA,EAAE0B,QAAQ,EAA2B,GAArC;IACnC,MAAM,CAACnB,cAAcC,gBAAgB,GAAGqB,CAAAA,GAAAA,OAAAA,QAAQ,EAC9C;IAGF,MAAM,CAACC,kBAAkBC,oBAAoB,GAAGF,CAAAA,GAAAA,OAAAA,QAAQ,EAAC;IACzD,MAAMG,iBAAiBC,CAAAA,GAAAA,OAAAA,WAAW,EAChC,IAAMF,oBAAoB,CAACG,OAASA,OAAO,IAC3C,EAAE;IAGJ,MAAMC,2BAA2BF,CAAAA,GAAAA,OAAAA,WAAW,EAC1C,CAAC5B;QACC,IAAIA,SAAS,MAAM;YACjB2B;QACF;QACAxB,gBAAgBH;IAClB,GACA;QAAC2B;KAAe;IAGlB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACL,oBAAoBS,QAAQ,EAAA;QAE3BC,OAAO;YACL9B;YACAC,iBAAiB2B;QACnB;kBAECT;OANII;AASX;AAEO,SAAS3B;IACd,OAAOmC,CAAAA,GAAAA,OAAAA,UAAU,EAACX;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3404, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/app-dev-overlay-error-boundary.tsx"], "sourcesContent": ["import { PureComponent } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { RuntimeErrorHandler } from '../../../client/dev/runtime-error-handler'\nimport { ErrorBoundary } from '../../../client/components/error-boundary'\nimport DefaultGlobalError from '../../../client/components/builtin/global-error'\nimport type { GlobalErrorState } from '../../../client/components/app-router-instance'\nimport { SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE } from './segment-explorer-node'\n\ntype AppDevOverlayErrorBoundaryProps = {\n  children: React.ReactNode\n  globalError: GlobalErrorState\n}\n\ntype AppDevOverlayErrorBoundaryState = {\n  reactError: unknown\n}\n\nfunction ErroredHtml({\n  globalError: [GlobalError, globalErrorStyles],\n  error,\n}: {\n  globalError: GlobalErrorState\n  error: unknown\n}) {\n  if (!error) {\n    return (\n      <html>\n        <head />\n        <body />\n      </html>\n    )\n  }\n  return (\n    <ErrorBoundary errorComponent={DefaultGlobalError}>\n      {globalErrorStyles}\n      <GlobalError error={error} />\n    </ErrorBoundary>\n  )\n}\n\nexport class AppDevOverlayErrorBoundary extends PureComponent<\n  AppDevOverlayErrorBoundaryProps,\n  AppDevOverlayErrorBoundaryState\n> {\n  state = { reactError: null }\n\n  static getDerivedStateFromError(error: Error) {\n    RuntimeErrorHandler.hadRuntimeError = true\n\n    return {\n      reactError: error,\n    }\n  }\n\n  componentDidCatch(err: Error) {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      err.message === SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE\n    ) {\n      return\n    }\n    dispatcher.openErrorOverlay()\n  }\n\n  render() {\n    const { children, globalError } = this.props\n    const { reactError } = this.state\n\n    const fallback = (\n      <ErroredHtml globalError={globalError} error={reactError} />\n    )\n\n    return reactError !== null ? fallback : children\n  }\n}\n"], "names": ["AppDevOverlayErrorBoundary", "ErroredHtml", "globalError", "GlobalError", "globalErrorStyles", "error", "html", "head", "body", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "DefaultGlobalError", "PureComponent", "getDerivedStateFromError", "RuntimeError<PERSON>andler", "hadRuntimeError", "reactError", "componentDidCatch", "err", "process", "env", "NODE_ENV", "message", "SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE", "dispatcher", "openErrorOverlay", "render", "children", "props", "state", "fallback"], "mappings": ";;;+BAwCaA,8BAAAA;;;eAAAA;;;;;uBAxCiB;8BACH;qCACS;+BACN;sEACC;qCAE0B;AAWzD,SAASC,YAAY,KAMpB;IANoB,IAAA,EACnBC,aAAa,CAACC,aAAaC,kBAAkB,EAC7CC,KAAK,EAIN,GANoB;IAOnB,IAAI,CAACA,OAAO;QACV,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACC,QAAAA;;8BACC,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA,CAAAA;8BACD,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA,CAAAA;;;IAGP;IACA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACC,eAAAA,aAAa,EAAA;QAACC,gBAAgBC,aAAAA,OAAkB;;YAC9CP;0BACD,CAAA,GAAA,YAAA,GAAA,EAACD,aAAAA;gBAAYE,OAAOA;;;;AAG1B;AAEO,MAAML,mCAAmCY,OAAAA,aAAa;IAM3D,OAAOC,yBAAyBR,KAAY,EAAE;QAC5CS,qBAAAA,mBAAmB,CAACC,eAAe,GAAG;QAEtC,OAAO;YACLC,YAAYX;QACd;IACF;IAEAY,kBAAkBC,GAAU,EAAE;QAC5B,IACEC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBH,IAAII,OAAO,KAAKC,qBAAAA,wCAAwC,EACxD;YACA;QACF;QACAC,cAAAA,UAAU,CAACC,gBAAgB;IAC7B;IAEAC,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEzB,WAAW,EAAE,GAAG,IAAI,CAAC0B,KAAK;QAC5C,MAAM,EAAEZ,UAAU,EAAE,GAAG,IAAI,CAACa,KAAK;QAEjC,MAAMC,WAAAA,WAAAA,GACJ,CAAA,GAAA,YAAA,GAAA,EAAC7B,aAAAA;YAAYC,aAAaA;YAAaG,OAAOW;;QAGhD,OAAOA,eAAe,OAAOc,WAAWH;IAC1C;;QAjCK,KAAA,IAAA,OAAA,IAAA,CAILE,KAAAA,GAAQ;YAAEb,YAAY;QAAK;;AA8B7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3480, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/next-devtools/userspace/app/errors/use-forward-console-log.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { isTerminalLoggingEnabled, logQueue } from '../forward-logs'\nimport type { useWebsocket } from '../../../../client/dev/hot-reloader/app/use-websocket'\n\nexport const useForwardConsoleLog = (\n  socketRef: ReturnType<typeof useWebsocket>\n) => {\n  useEffect(() => {\n    if (!isTerminalLoggingEnabled) {\n      return\n    }\n    const socket = socketRef.current\n    if (!socket) {\n      return\n    }\n\n    const onOpen = () => {\n      logQueue.onSocketReady(socket)\n    }\n    socket.addEventListener('open', onOpen)\n\n    return () => {\n      socket.removeEventListener('open', onOpen)\n    }\n  }, [socketRef])\n}\n"], "names": ["useForwardConsoleLog", "socketRef", "useEffect", "isTerminalLoggingEnabled", "socket", "current", "onOpen", "logQueue", "onSocketReady", "addEventListener", "removeEventListener"], "mappings": ";;;+BAIaA,wBAAAA;;;eAAAA;;;uBAJa;6BACyB;AAG5C,MAAMA,uBAAuB,CAClCC;IAEAC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,IAAI,CAACC,aAAAA,wBAAwB,EAAE;YAC7B;QACF;QACA,MAAMC,SAASH,UAAUI,OAAO;QAChC,IAAI,CAACD,QAAQ;YACX;QACF;QAEA,MAAME,SAAS;YACbC,aAAAA,QAAQ,CAACC,aAAa,CAACJ;QACzB;QACAA,OAAOK,gBAAgB,CAAC,QAAQH;QAEhC,OAAO;YACLF,OAAOM,mBAAmB,CAAC,QAAQJ;QACrC;IACF,GAAG;QAACL;KAAU;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3522, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/lib/framework/boundary-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IACAC,yBAAyB,EAAA;eAAzBA;;IAFAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMH,yBAAyB;AAC/B,MAAMG,yBAAyB;AAC/B,MAAMF,uBAAuB;AAC7B,MAAMC,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3559, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAACE;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAACG;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3624, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/lib/framework/boundary-components.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from './boundary-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [ROOT_LAYOUT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n\nexport const RootLayoutBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[\n    ROOT_LAYOUT_BOUNDARY_NAME.slice(0) as typeof ROOT_LAYOUT_BOUNDARY_NAME\n  ]\n"], "names": ["MetadataBoundary", "OutletBoundary", "RootLayoutBoundary", "ViewportBoundary", "NameSpace", "METADATA_BOUNDARY_NAME", "children", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "slice"], "mappings": ";;;;;;;;;;;;;;;;IA+BaA,gBAAgB,EAAA;eAAhBA;;IAUAC,cAAc,EAAA;eAAdA;;IAKAC,kBAAkB,EAAA;eAAlBA;;IAVAC,gBAAgB,EAAA;eAAhBA;;;mCA5BN;AAEP,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,EAAEC,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,EAAED,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACE,mBAAAA,oBAAoB,CAAC,EAAE,SAAU,EAAEF,QAAQ,EAA2B;QACrE,OAAOA;IACT;IACA,CAACG,mBAAAA,yBAAyB,CAAC,EAAE,SAAU,EACrCH,QAAQ,EAGT;QACC,OAAOA;IACT;AACF;AAEO,MAAMN,mBACX,AACA,4DAA4D,oBADoB;AAEhFI,SAAS,CAACC,mBAAAA,sBAAsB,CAACK,KAAK,CAAC,GAAoC;AAEtE,MAAMP,mBACX,AACA,4DAA4D,oBADoB;AAEhFC,SAAS,CAACG,mBAAAA,sBAAsB,CAACG,KAAK,CAAC,GAAoC;AAEtE,MAAMT,iBACX,AACA,4DAA4D,oBADoB;AAEhFG,SAAS,CAACI,mBAAAA,oBAAoB,CAACE,KAAK,CAAC,GAAkC;AAElE,MAAMR,qBACX,AACA,4DAA4D,oBADoB;AAEhFE,SAAS,CACPK,mBAAAA,yBAAyB,CAACC,KAAK,CAAC,GACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3682, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/node_modules/next/src/lib/is-error.ts"], "sourcesContent": ["import { isPlainObject } from '../shared/lib/is-plain-object'\n\n// We allow some additional attached properties for Next.js errors\nexport interface NextError extends Error {\n  type?: string\n  page?: string\n  code?: string | number\n  cancelled?: boolean\n  digest?: number\n}\n\n/**\n * Checks whether the given value is a NextError.\n * This can be used to print a more detailed error message with properties like `code` & `digest`.\n */\nexport default function isError(err: unknown): err is NextError {\n  return (\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\n  )\n}\n\nfunction safeStringify(obj: any) {\n  const seen = new WeakSet()\n\n  return JSON.stringify(obj, (_key, value) => {\n    // If value is an object and already seen, replace with \"[Circular]\"\n    if (typeof value === 'object' && value !== null) {\n      if (seen.has(value)) {\n        return '[Circular]'\n      }\n      seen.add(value)\n    }\n    return value\n  })\n}\n\nexport function getProperError(err: unknown): Error {\n  if (isError(err)) {\n    return err\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // provide better error for case where `throw undefined`\n    // is called in development\n    if (typeof err === 'undefined') {\n      return new Error(\n        'An undefined error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n\n    if (err === null) {\n      return new Error(\n        'A null error was thrown, ' +\n          'see here for more info: https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n  }\n\n  return new Error(isPlainObject(err) ? safeStringify(err) : err + '')\n}\n"], "names": ["isError", "getProperError", "err", "safeStringify", "obj", "seen", "WeakSet", "JSON", "stringify", "_key", "value", "has", "add", "process", "env", "NODE_ENV", "Error", "isPlainObject"], "mappings": ";;;;;;;;;;;;;;IAWA;;;CAGC,GACD,OAIC,EAAA;eAJuBA;;IAqBRC,cAAc,EAAA;eAAdA;;;+BApCc;AAef,SAASD,QAAQE,GAAY;IAC1C,OACE,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,UAAUA,OAAO,aAAaA;AAE7E;AAEA,SAASC,cAAcC,GAAQ;IAC7B,MAAMC,OAAO,IAAIC;IAEjB,OAAOC,KAAKC,SAAS,CAACJ,KAAK,CAACK,MAAMC;QAChC,oEAAoE;QACpE,IAAI,OAAOA,UAAU,YAAYA,UAAU,MAAM;YAC/C,IAAIL,KAAKM,GAAG,CAACD,QAAQ;gBACnB,OAAO;YACT;YACAL,KAAKO,GAAG,CAACF;QACX;QACA,OAAOA;IACT;AACF;AAEO,SAAST,eAAeC,GAAY;IACzC,IAAIF,QAAQE,MAAM;QAChB,OAAOA;IACT;IAEA,IAAIW,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;QAC1C,wDAAwD;QACxD,2BAA2B;QAC3B,IAAI,OAAOb,QAAQ,aAAa;YAC9B,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,oCACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;QAEA,IAAId,QAAQ,MAAM;YAChB,OAAO,OAAA,cAGN,CAHM,IAAIc,MACT,8BACE,6EAFG,qBAAA;uBAAA;4BAAA;8BAAA;YAGP;QACF;IACF;IAEA,OAAO,OAAA,cAA6D,CAA7D,IAAIA,MAAMC,CAAAA,GAAAA,eAAAA,aAAa,EAACf,OAAOC,cAAcD,OAAOA,MAAM,KAA1D,qBAAA;eAAA;oBAAA;sBAAA;IAA4D;AACrE", "ignoreList": [0], "debugId": null}}]}