{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/git/trackify/src/components/AppLayout.tsx <module evaluation>\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,2RAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/git/trackify/src/components/AppLayout.tsx\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,2RAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,yLAAO,EAAC,IAAA,gKAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,0LAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,2LAAI,GAAG;IAE9B,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/app/clients/page.tsx"], "sourcesContent": ["import { AppLayout } from '@/components/AppLayout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Plus, Users, Building, DollarSign, Clock } from 'lucide-react'\n\nexport default function Clients() {\n  return (\n    <AppLayout>\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-3xl font-bold tracking-tight\">Clients</h1>\n            <p className=\"text-muted-foreground\">\n              Gérez votre base de clients et leurs informations\n            </p>\n          </div>\n          <Button className=\"gap-2\">\n            <Plus className=\"h-4 w-4\" />\n            Nouveau client\n          </Button>\n        </div>\n\n        {/* Statistiques des clients */}\n        <div className=\"grid gap-4 md:grid-cols-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Total clients\n              </CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Tous vos clients\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Clients actifs\n              </CardTitle>\n              <Building className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Avec projets en cours\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Revenus totaux\n              </CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0€</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Tous les clients\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Temps total\n              </CardTitle>\n              <Clock className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0h</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Temps travaillé\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Liste des clients */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Vos clients</CardTitle>\n            <CardDescription>\n              Liste de tous vos clients avec leurs informations et projets\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center py-12\">\n              <Users className=\"mx-auto h-12 w-12 text-muted-foreground/50\" />\n              <h3 className=\"mt-4 text-lg font-semibold\">Aucun client</h3>\n              <p className=\"mt-2 text-sm text-muted-foreground\">\n                Vous n'avez pas encore ajouté de client.\n                <br />\n                Commencez par ajouter votre premier client pour organiser vos projets.\n              </p>\n              <Button className=\"mt-4 gap-2\">\n                <Plus className=\"h-4 w-4\" />\n                Ajouter mon premier client\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </AppLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,iQAAC,+JAAS;kBACR,cAAA,iQAAC;YAAI,WAAU;;8BACb,iQAAC;oBAAI,WAAU;;sCACb,iQAAC;;8CACC,iQAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,iQAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,iQAAC,+JAAM;4BAAC,WAAU;;8CAChB,iQAAC,6NAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;8BAMhC,iQAAC;oBAAI,WAAU;;sCACb,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,gOAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,yOAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,mPAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,gOAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,iQAAC,2JAAI;;sCACH,iQAAC,iKAAU;;8CACT,iQAAC,gKAAS;8CAAC;;;;;;8CACX,iQAAC,sKAAe;8CAAC;;;;;;;;;;;;sCAInB,iQAAC,kKAAW;sCACV,cAAA,iQAAC;gCAAI,WAAU;;kDACb,iQAAC,gOAAK;wCAAC,WAAU;;;;;;kDACjB,iQAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,iQAAC;wCAAE,WAAU;;4CAAqC;0DAEhD,iQAAC;;;;;4CAAK;;;;;;;kDAGR,iQAAC,+JAAM;wCAAC,WAAU;;0DAChB,iQAAC,6NAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C", "debugId": null}}]}