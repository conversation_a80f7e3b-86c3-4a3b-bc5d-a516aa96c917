{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/git/trackify/src/components/AppLayout.tsx <module evaluation>\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,2RAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/git/trackify/src/components/AppLayout.tsx\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,2RAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,yLAAO,EAAC,IAAA,gKAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/app/dashboard/page.tsx"], "sourcesContent": ["import { AppLayout } from '@/components/AppLayout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { BarChart3, Clock, DollarSign, TrendingUp } from 'lucide-react'\n\nexport default function Dashboard() {\n  return (\n    <AppLayout>\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Vue d'ensemble de votre activité et de vos revenus\n          </p>\n        </div>\n\n        {/* Statistiques principales */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Heures cette semaine\n              </CardTitle>\n              <Clock className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0h 00m</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +0% par rapport à la semaine dernière\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Revenus cette semaine\n              </CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0€</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +0% par rapport à la semaine dernière\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Temps billable\n              </CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0%</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Pourcentage du temps total\n              </p>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">\n                Projets actifs\n              </CardTitle>\n              <BarChart3 className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Projets en cours\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Graphiques et détails */}\n        <div className=\"grid gap-4 md:grid-cols-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Activité hebdomadaire</CardTitle>\n              <CardDescription>\n                Répartition de votre temps par jour\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8 text-muted-foreground\">\n                Graphique à venir\n                <br />\n                <small>Données insuffisantes pour afficher le graphique</small>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardHeader>\n              <CardTitle>Projets les plus actifs</CardTitle>\n              <CardDescription>\n                Temps passé par projet cette semaine\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center py-8 text-muted-foreground\">\n                Aucun projet actif\n                <br />\n                <small>Créez votre premier projet pour commencer</small>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Résumé des revenus */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Résumé des revenus</CardTitle>\n            <CardDescription>\n              Détail de vos gains par client et projet\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center py-8 text-muted-foreground\">\n              Aucune donnée de revenus disponible\n              <br />\n              <small>Configurez les taux horaires pour vos projets</small>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </AppLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,iQAAC,+JAAS;kBACR,cAAA,iQAAC;YAAI,WAAU;;8BACb,iQAAC;;sCACC,iQAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,iQAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,iQAAC;oBAAI,WAAU;;sCACb,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,gOAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,mPAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,mPAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;oCAAC,WAAU;;sDACpB,iQAAC,gKAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,iQAAC,kPAAS;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,iQAAC,kKAAW;;sDACV,iQAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,iQAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,iQAAC;oBAAI,WAAU;;sCACb,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;;sDACT,iQAAC,gKAAS;sDAAC;;;;;;sDACX,iQAAC,sKAAe;sDAAC;;;;;;;;;;;;8CAInB,iQAAC,kKAAW;8CACV,cAAA,iQAAC;wCAAI,WAAU;;4CAAyC;0DAEtD,iQAAC;;;;;0DACD,iQAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;sCAKb,iQAAC,2JAAI;;8CACH,iQAAC,iKAAU;;sDACT,iQAAC,gKAAS;sDAAC;;;;;;sDACX,iQAAC,sKAAe;sDAAC;;;;;;;;;;;;8CAInB,iQAAC,kKAAW;8CACV,cAAA,iQAAC;wCAAI,WAAU;;4CAAyC;0DAEtD,iQAAC;;;;;0DACD,iQAAC;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOf,iQAAC,2JAAI;;sCACH,iQAAC,iKAAU;;8CACT,iQAAC,gKAAS;8CAAC;;;;;;8CACX,iQAAC,sKAAe;8CAAC;;;;;;;;;;;;sCAInB,iQAAC,kKAAW;sCACV,cAAA,iQAAC;gCAAI,WAAU;;oCAAyC;kDAEtD,iQAAC;;;;;kDACD,iQAAC;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}]}