{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/git/trackify/src/components/AppLayout.tsx <module evaluation>\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,2RAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/git/trackify/src/components/AppLayout.tsx\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,2RAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/app/page.tsx"], "sourcesContent": ["import { AppLayout } from '@/components/AppLayout'\nimport { TimeTracker } from '@/components/TimeTracker'\n\nexport default function Home() {\n  return (\n    <AppLayout>\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Suivi du temps</h1>\n          <p className=\"text-muted-foreground\">\n            G<PERSON>rez votre temps de travail efficacement\n          </p>\n        </div>\n\n        <TimeTracker />\n      </div>\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;AAGe,SAAS;IACtB,qBACE,iQAAC,+JAAS;kBACR,cAAA,iQAAC;YAAI,WAAU;;8BACb,iQAAC;;sCACC,iQAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,iQAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,iQAAC;;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}