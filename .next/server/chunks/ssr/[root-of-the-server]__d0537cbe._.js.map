{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,yLAAO,EAAC,IAAA,gKAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,0LAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,2LAAI,GAAG;IAE9B,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,iQAAC,6LAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,iQAAC,8LAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,iQAAC,iMAAwB;QACvB,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,iQAAC,uMAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,iQAAC,yMAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,iQAAC,0MAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,iQAAC,yMAA4B;kBAC3B,cAAA,iQAAC,0MAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,4IAAE,EACX,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,iQAAC,wMAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,iQAAC,uMAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,4IAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,iQAAC,+MAAkC;QACjC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,iQAAC;gBAAK,WAAU;0BACd,cAAA,iQAAC,gNAAmC;8BAClC,cAAA,iQAAC,wOAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,iQAAC,6MAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,iQAAC,4MAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,iQAAC;gBAAK,WAAU;0BACd,cAAA,iQAAC,gNAAmC;8BAClC,cAAA,iQAAC,2OAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,iQAAC,wMAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,4IAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,iQAAC,4MAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,iQAAC,sMAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,iQAAC,6MAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,4IAAE,EACX,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,iQAAC,iQAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,iQAAC,6MAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { CloudronUser } from '@/lib/auth'\n\nexport function useAuth() {\n  const [user, setUser] = useState<CloudronUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Récupérer les informations utilisateur depuis l'API\n    fetch('/api/auth/user')\n      .then(res => res.json())\n      .then(data => {\n        setUser(data.user || null)\n        setLoading(false)\n      })\n      .catch(error => {\n        console.error('Erreur lors de la récupération de l\\'utilisateur:', error)\n        setUser(null)\n        setLoading(false)\n      })\n  }, [])\n\n  return {\n    user,\n    loading,\n    isAuthenticated: !!user\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAKO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,oOAAQ,EAAsB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,oOAAQ,EAAC;IAEvC,IAAA,qOAAS,EAAC;QACR,sDAAsD;QACtD,MAAM,kBACH,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,IACpB,IAAI,CAAC,CAAA;YACJ,QAAQ,KAAK,IAAI,IAAI;YACrB,WAAW;QACb,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,qDAAqD;YACnE,QAAQ;YACR,WAAW;QACb;IACJ,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;AACF", "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { \n  DropdownMenu, \n  DropdownMenuContent, \n  DropdownMenuItem, \n  DropdownMenuTrigger \n} from '@/components/ui/dropdown-menu'\nimport { useAuth } from '@/hooks/useAuth'\nimport { \n  Clock, \n  BarChart3, \n  FolderOpen, \n  Users, \n  User,\n  LogOut \n} from 'lucide-react'\n\nconst navigationItems = [\n  {\n    name: 'Temps',\n    href: '/',\n    icon: Clock,\n  },\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: BarChart3,\n  },\n  {\n    name: 'Projets',\n    href: '/projects',\n    icon: FolderOpen,\n  },\n  {\n    name: 'Clients',\n    href: '/clients',\n    icon: Users,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container flex h-14 items-center\">\n          <div className=\"mr-4 hidden md:flex\">\n            <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n              <Clock className=\"h-6 w-6\" />\n              <span className=\"hidden font-bold sm:inline-block\">Trackify</span>\n            </Link>\n          </div>\n          <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n            <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n              <div className=\"h-8 w-8 rounded-full bg-muted animate-pulse\" />\n            </div>\n          </div>\n        </div>\n      </nav>\n    )\n  }\n\n  return (\n    <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <div className=\"mr-4 hidden md:flex\">\n          <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n            <Clock className=\"h-6 w-6\" />\n            <span className=\"hidden font-bold sm:inline-block\">Trackify</span>\n          </Link>\n          <nav className=\"flex items-center space-x-6 text-sm font-medium\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    \"flex items-center space-x-2 transition-colors hover:text-foreground/80\",\n                    pathname === item.href ? \"text-foreground\" : \"text-foreground/60\"\n                  )}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              )\n            })}\n          </nav>\n        </div>\n        <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n          <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n            {user && (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarFallback>\n                        {user.displayName.charAt(0).toUpperCase()}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuItem className=\"flex flex-col items-start\">\n                    <div className=\"font-medium\">{user.displayName}</div>\n                    <div className=\"text-xs text-muted-foreground\">{user.email}</div>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <User className=\"mr-2 h-4 w-4\" />\n                    <span>Profil</span>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    <span>Déconnexion</span>\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            )}\n          </div>\n        </div>\n      </div>\n      \n      {/* Navigation mobile */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-around border-t bg-background p-2\">\n          {navigationItems.map((item) => {\n            const Icon = item.icon\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  \"flex flex-col items-center space-y-1 rounded-md p-2 text-xs transition-colors hover:text-foreground/80\",\n                  pathname === item.href ? \"text-foreground bg-muted\" : \"text-foreground/60\"\n                )}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{item.name}</span>\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;AAuBA,MAAM,kBAAkB;IACtB;QACE,MAAM;QACN,MAAM;QACN,MAAM,gOAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kPAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mPAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gOAAK;IACb;CACD;AAEM,SAAS;IACd,MAAM,WAAW,IAAA,oKAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,qJAAO;IAEjC,IAAI,SAAS;QACX,qBACE,iQAAC;YAAI,WAAU;sBACb,cAAA,iQAAC;gBAAI,WAAU;;kCACb,iQAAC;wBAAI,WAAU;kCACb,cAAA,iQAAC,0LAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,iQAAC,gOAAK;oCAAC,WAAU;;;;;;8CACjB,iQAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAGvD,iQAAC;wBAAI,WAAU;kCACb,cAAA,iQAAC;4BAAI,WAAU;sCACb,cAAA,iQAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,iQAAC;QAAI,WAAU;;0BACb,iQAAC;gBAAI,WAAU;;kCACb,iQAAC;wBAAI,WAAU;;0CACb,iQAAC,0LAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,iQAAC,gOAAK;wCAAC,WAAU;;;;;;kDACjB,iQAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,iQAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC;oCACpB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,iQAAC,0LAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,IAAA,4IAAE,EACX,0EACA,aAAa,KAAK,IAAI,GAAG,oBAAoB;;0DAG/C,iQAAC;gDAAK,WAAU;;;;;;0DAChB,iQAAC;0DAAM,KAAK,IAAI;;;;;;;uCARX,KAAK,IAAI;;;;;gCAWpB;;;;;;;;;;;;kCAGJ,iQAAC;wBAAI,WAAU;kCACb,cAAA,iQAAC;4BAAI,WAAU;sCACZ,sBACC,iQAAC,+KAAY;;kDACX,iQAAC,sLAAmB;wCAAC,OAAO;kDAC1B,cAAA,iQAAC,+JAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,iQAAC,+JAAM;gDAAC,WAAU;0DAChB,cAAA,iQAAC,uKAAc;8DACZ,KAAK,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;kDAK/C,iQAAC,sLAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,iQAAC,mLAAgB;gDAAC,WAAU;;kEAC1B,iQAAC;wDAAI,WAAU;kEAAe,KAAK,WAAW;;;;;;kEAC9C,iQAAC;wDAAI,WAAU;kEAAiC,KAAK,KAAK;;;;;;;;;;;;0DAE5D,iQAAC,mLAAgB;;kEACf,iQAAC,6NAAI;wDAAC,WAAU;;;;;;kEAChB,iQAAC;kEAAK;;;;;;;;;;;;0DAER,iQAAC,mLAAgB;;kEACf,iQAAC,uOAAM;wDAAC,WAAU;;;;;;kEAClB,iQAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,iQAAC;gBAAI,WAAU;0BACb,cAAA,iQAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,iQAAC,0LAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,IAAA,4IAAE,EACX,0GACA,aAAa,KAAK,IAAI,GAAG,6BAA6B;;8CAGxD,iQAAC;oCAAK,WAAU;;;;;;8CAChB,iQAAC;8CAAM,KAAK,IAAI;;;;;;;2BARX,KAAK,IAAI;;;;;oBAWpB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './Navigation'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport function AppLayout({ children }: AppLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Navigation />\n      <main className=\"container mx-auto py-6\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAQO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,qBACE,iQAAC;QAAI,WAAU;;0BACb,iQAAC,iKAAU;;;;;0BACX,iQAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,iQAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,iQAAC;QACC,aAAU;QACV,WAAW,IAAA,4IAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/TimeTracker.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Card } from '@/components/ui/card'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger\n} from '@/components/ui/dropdown-menu'\nimport {\n  Play,\n  Square,\n  Tag,\n  DollarSign,\n  MoreHorizontal,\n  Plus,\n  Circle\n} from 'lucide-react'\n\n// Types pour les données de l'API\ninterface Project {\n  id: number\n  name: string\n  clientName: string\n  color?: string\n}\n\ninterface TimeEntry {\n  id: number\n  projectId: number\n  projectName: string\n  clientName: string\n  description: string\n  startTime: string\n  endTime?: string\n  duration: number\n  isRunning: boolean\n  isBillable?: boolean\n}\n\n// Couleurs par défaut pour les projets\nconst projectColors = [\n  '#ef4444', '#3b82f6', '#10b981', '#f59e0b',\n  '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'\n]\n\nfunction getProjectColor(projectId: number): string {\n  return projectColors[projectId % projectColors.length]\n}\n\nfunction formatDuration(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const secs = seconds % 60\n  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n}\n\nfunction TimeEntryRow({ entry, onStop }: { entry: TimeEntry; onStop: (id: number) => void }) {\n  const [currentDuration, setCurrentDuration] = useState(entry.duration || 0)\n\n  useEffect(() => {\n    if (entry.isRunning) {\n      const interval = setInterval(() => {\n        const startTime = new Date(entry.startTime).getTime()\n        const now = Date.now()\n        const newDuration = Math.floor((now - startTime) / 1000)\n        setCurrentDuration(newDuration)\n      }, 1000)\n\n      return () => clearInterval(interval)\n    }\n  }, [entry.isRunning, entry.startTime])\n\n  const handleStopTimer = () => {\n    if (entry.isRunning) {\n      onStop(entry.id)\n    }\n  }\n\n  return (\n    <Card className=\"p-4\">\n      <div className=\"flex items-center gap-4\">\n        {/* Indicateur de couleur du projet */}\n        <div\n          className=\"w-3 h-3 rounded-full flex-shrink-0\"\n          style={{ backgroundColor: getProjectColor(entry.projectId) }}\n        />\n\n        {/* Nom du projet et client */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center gap-2\">\n            <span className=\"font-medium text-sm\">\n              {entry.projectName}\n            </span>\n            <span className=\"text-xs text-muted-foreground\">\n              - {entry.clientName}\n            </span>\n          </div>\n          <Input\n            placeholder=\"Ajouter une description...\"\n            defaultValue={entry.description}\n            className=\"mt-1 border-none p-0 h-auto text-sm bg-transparent focus-visible:ring-0\"\n          />\n        </div>\n\n        {/* Icône billable */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className={`p-1 h-8 w-8 ${entry.isBillable ? 'text-green-600' : 'text-muted-foreground'}`}\n        >\n          <Tag className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Icône dollar (pour les taux) */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"p-1 h-8 w-8 text-muted-foreground\"\n        >\n          <DollarSign className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Durée */}\n        <div className=\"text-lg font-mono font-medium min-w-[80px] text-right\">\n          {formatDuration(currentDuration)}\n        </div>\n\n        {/* Bouton Start/Stop */}\n        <Button\n          variant={entry.isRunning ? \"destructive\" : \"default\"}\n          size=\"sm\"\n          className=\"min-w-[70px]\"\n          onClick={handleStopTimer}\n        >\n          {entry.isRunning ? (\n            <>\n              <Square className=\"h-4 w-4 mr-1\" />\n              STOP\n            </>\n          ) : (\n            <>\n              <Play className=\"h-4 w-4 mr-1\" />\n              START\n            </>\n          )}\n        </Button>\n\n        {/* Menu options */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" size=\"sm\" className=\"p-1 h-8 w-8\">\n              <MoreHorizontal className=\"h-4 w-4\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\">\n            <DropdownMenuItem>Modifier</DropdownMenuItem>\n            <DropdownMenuItem>Dupliquer</DropdownMenuItem>\n            <DropdownMenuItem className=\"text-destructive\">\n              Supprimer\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </Card>\n  )\n}\n\nfunction NewTimeEntryRow({ projects, onStart }: { projects: Project[]; onStart: (projectId: number, description: string) => void }) {\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null)\n  const [description, setDescription] = useState('')\n\n  const handleStart = () => {\n    if (selectedProject) {\n      onStart(selectedProject.id, description)\n      setDescription('')\n    }\n  }\n\n  return (\n    <Card className=\"p-4 border-dashed\">\n      <div className=\"flex items-center gap-4\">\n        {/* Sélecteur de projet */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"flex items-center gap-2 h-8\">\n              {selectedProject ? (\n                <>\n                  <div\n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: getProjectColor(selectedProject.id) }}\n                  />\n                  <span className=\"text-sm\">\n                    {selectedProject.name} - {selectedProject.clientName}\n                  </span>\n                </>\n              ) : (\n                <>\n                  <Circle className=\"h-3 w-3\" />\n                  <span className=\"text-sm text-muted-foreground\">\n                    Choisir un projet\n                  </span>\n                </>\n              )}\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-64\">\n            {projects.map((project) => (\n              <DropdownMenuItem\n                key={project.id}\n                onClick={() => setSelectedProject(project)}\n                className=\"flex items-center gap-2\"\n              >\n                <div\n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: getProjectColor(project.id) }}\n                />\n                <span>{project.name} - {project.clientName}</span>\n              </DropdownMenuItem>\n            ))}\n            <DropdownMenuItem className=\"flex items-center gap-2 text-blue-600\">\n              <Plus className=\"h-3 w-3\" />\n              <span>Nouveau projet</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n\n        {/* Description */}\n        <div className=\"flex-1\">\n          <Input\n            placeholder=\"Que faites-vous ?\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            className=\"border-none p-0 h-auto text-sm bg-transparent focus-visible:ring-0\"\n          />\n        </div>\n\n        {/* Icônes et bouton start */}\n        <Button variant=\"ghost\" size=\"sm\" className=\"p-1 h-8 w-8 text-muted-foreground\">\n          <Tag className=\"h-4 w-4\" />\n        </Button>\n\n        <Button variant=\"ghost\" size=\"sm\" className=\"p-1 h-8 w-8 text-muted-foreground\">\n          <DollarSign className=\"h-4 w-4\" />\n        </Button>\n\n        <div className=\"text-lg font-mono font-medium min-w-[80px] text-right text-muted-foreground\">\n          00:00:00\n        </div>\n\n        <Button\n          disabled={!selectedProject}\n          size=\"sm\"\n          className=\"min-w-[70px]\"\n          onClick={handleStart}\n        >\n          <Play className=\"h-4 w-4 mr-1\" />\n          START\n        </Button>\n\n        <div className=\"w-8\" /> {/* Espace pour aligner avec les autres lignes */}\n      </div>\n    </Card>\n  )\n}\n\nexport function TimeTracker() {\n  const [projects, setProjects] = useState<Project[]>([])\n  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Charger les projets et entrées de temps\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // Charger les projets\n        const projectsRes = await fetch('/api/projects')\n        if (projectsRes.ok) {\n          const projectsData = await projectsRes.json()\n          setProjects(projectsData.projects || [])\n        }\n\n        // Charger les entrées de temps\n        const entriesRes = await fetch('/api/time-entries')\n        if (entriesRes.ok) {\n          const entriesData = await entriesRes.json()\n          setTimeEntries(entriesData.timeEntries || [])\n        }\n      } catch (error) {\n        console.error('Erreur lors du chargement des données:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  const handleStartTimer = async (projectId: number, description: string) => {\n    try {\n      const response = await fetch('/api/time-entries/start', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ projectId, description })\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setTimeEntries(prev => [data.timeEntry, ...prev])\n      }\n    } catch (error) {\n      console.error('Erreur lors du démarrage du timer:', error)\n    }\n  }\n\n  const handleStopTimer = async (entryId: number) => {\n    try {\n      const response = await fetch(`/api/time-entries/${entryId}/stop`, {\n        method: 'POST'\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setTimeEntries(prev =>\n          prev.map(entry =>\n            entry.id === entryId ? data.timeEntry : entry\n          )\n        )\n      }\n    } catch (error) {\n      console.error('Erreur lors de l\\'arrêt du timer:', error)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        <Card className=\"p-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-4 bg-muted rounded w-1/4 mb-2\"></div>\n            <div className=\"h-8 bg-muted rounded\"></div>\n          </div>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Nouvelle entrée */}\n      <NewTimeEntryRow projects={projects} onStart={handleStartTimer} />\n\n      {/* Entrées existantes */}\n      {timeEntries.map((entry) => (\n        <TimeEntryRow key={entry.id} entry={entry} onStop={handleStopTimer} />\n      ))}\n\n      {/* Message si aucune entrée */}\n      {timeEntries.length === 0 && (\n        <div className=\"text-center py-8 text-muted-foreground\">\n          <p>Aucune entrée de temps aujourd'hui.</p>\n          <p className=\"text-sm\">Commencez par sélectionner un projet et démarrer le timer !</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;AA4CA,uCAAuC;AACvC,MAAM,gBAAgB;IACpB;IAAW;IAAW;IAAW;IACjC;IAAW;IAAW;IAAW;CAClC;AAED,SAAS,gBAAgB,SAAiB;IACxC,OAAO,aAAa,CAAC,YAAY,cAAc,MAAM,CAAC;AACxD;AAEA,SAAS,eAAe,OAAe;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,UAAU;IACvB,OAAO,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AAC1H;AAEA,SAAS,aAAa,EAAE,KAAK,EAAE,MAAM,EAAsD;IACzF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,oOAAQ,EAAC,MAAM,QAAQ,IAAI;IAEzE,IAAA,qOAAS,EAAC;QACR,IAAI,MAAM,SAAS,EAAE;YACnB,MAAM,WAAW,YAAY;gBAC3B,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS,EAAE,OAAO;gBACnD,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,MAAM,SAAS,IAAI;gBACnD,mBAAmB;YACrB,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC,MAAM,SAAS;QAAE,MAAM,SAAS;KAAC;IAErC,MAAM,kBAAkB;QACtB,IAAI,MAAM,SAAS,EAAE;YACnB,OAAO,MAAM,EAAE;QACjB;IACF;IAEA,qBACE,iQAAC,2JAAI;QAAC,WAAU;kBACd,cAAA,iQAAC;YAAI,WAAU;;8BAEb,iQAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,gBAAgB,MAAM,SAAS;oBAAE;;;;;;8BAI7D,iQAAC;oBAAI,WAAU;;sCACb,iQAAC;4BAAI,WAAU;;8CACb,iQAAC;oCAAK,WAAU;8CACb,MAAM,WAAW;;;;;;8CAEpB,iQAAC;oCAAK,WAAU;;wCAAgC;wCAC3C,MAAM,UAAU;;;;;;;;;;;;;sCAGvB,iQAAC,6JAAK;4BACJ,aAAY;4BACZ,cAAc,MAAM,WAAW;4BAC/B,WAAU;;;;;;;;;;;;8BAKd,iQAAC,+JAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAW,CAAC,YAAY,EAAE,MAAM,UAAU,GAAG,mBAAmB,yBAAyB;8BAEzF,cAAA,iQAAC,0NAAG;wBAAC,WAAU;;;;;;;;;;;8BAIjB,iQAAC,+JAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;8BAEV,cAAA,iQAAC,mPAAU;wBAAC,WAAU;;;;;;;;;;;8BAIxB,iQAAC;oBAAI,WAAU;8BACZ,eAAe;;;;;;8BAIlB,iQAAC,+JAAM;oBACL,SAAS,MAAM,SAAS,GAAG,gBAAgB;oBAC3C,MAAK;oBACL,WAAU;oBACV,SAAS;8BAER,MAAM,SAAS,iBACd;;0CACE,iQAAC,mOAAM;gCAAC,WAAU;;;;;;4BAAiB;;qDAIrC;;0CACE,iQAAC,6NAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;8BAOvC,iQAAC,+KAAY;;sCACX,iQAAC,sLAAmB;4BAAC,OAAO;sCAC1B,cAAA,iQAAC,+JAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,iQAAC,qPAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG9B,iQAAC,sLAAmB;4BAAC,OAAM;;8CACzB,iQAAC,mLAAgB;8CAAC;;;;;;8CAClB,iQAAC,mLAAgB;8CAAC;;;;;;8CAClB,iQAAC,mLAAgB;oCAAC,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;AAEA,SAAS,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAsF;IAChI,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,oOAAQ,EAAiB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,oOAAQ,EAAC;IAE/C,MAAM,cAAc;QAClB,IAAI,iBAAiB;YACnB,QAAQ,gBAAgB,EAAE,EAAE;YAC5B,eAAe;QACjB;IACF;IAEA,qBACE,iQAAC,2JAAI;QAAC,WAAU;kBACd,cAAA,iQAAC;YAAI,WAAU;;8BAEb,iQAAC,+KAAY;;sCACX,iQAAC,sLAAmB;4BAAC,OAAO;sCAC1B,cAAA,iQAAC,+JAAM;gCAAC,SAAQ;gCAAQ,WAAU;0CAC/B,gCACC;;sDACE,iQAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,gBAAgB,gBAAgB,EAAE;4CAAE;;;;;;sDAEhE,iQAAC;4CAAK,WAAU;;gDACb,gBAAgB,IAAI;gDAAC;gDAAI,gBAAgB,UAAU;;;;;;;;iEAIxD;;sDACE,iQAAC,mOAAM;4CAAC,WAAU;;;;;;sDAClB,iQAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAOxD,iQAAC,sLAAmB;4BAAC,WAAU;;gCAC5B,SAAS,GAAG,CAAC,CAAC,wBACb,iQAAC,mLAAgB;wCAEf,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,iQAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,gBAAgB,QAAQ,EAAE;gDAAE;;;;;;0DAExD,iQAAC;;oDAAM,QAAQ,IAAI;oDAAC;oDAAI,QAAQ,UAAU;;;;;;;;uCARrC,QAAQ,EAAE;;;;;8CAWnB,iQAAC,mLAAgB;oCAAC,WAAU;;sDAC1B,iQAAC,6NAAI;4CAAC,WAAU;;;;;;sDAChB,iQAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,iQAAC;oBAAI,WAAU;8BACb,cAAA,iQAAC,6JAAK;wBACJ,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;;;;;;8BAKd,iQAAC,+JAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;8BAC1C,cAAA,iQAAC,0NAAG;wBAAC,WAAU;;;;;;;;;;;8BAGjB,iQAAC,+JAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;8BAC1C,cAAA,iQAAC,mPAAU;wBAAC,WAAU;;;;;;;;;;;8BAGxB,iQAAC;oBAAI,WAAU;8BAA8E;;;;;;8BAI7F,iQAAC,+JAAM;oBACL,UAAU,CAAC;oBACX,MAAK;oBACL,WAAU;oBACV,SAAS;;sCAET,iQAAC,6NAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAInC,iQAAC;oBAAI,WAAU;;;;;;gBAAQ;;;;;;;;;;;;AAI/B;AAEO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,oOAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,oOAAQ,EAAc,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,oOAAQ,EAAC;IAEvC,0CAA0C;IAC1C,IAAA,qOAAS,EAAC;QACR,MAAM,WAAW;YACf,IAAI;gBACF,sBAAsB;gBACtB,MAAM,cAAc,MAAM,MAAM;gBAChC,IAAI,YAAY,EAAE,EAAE;oBAClB,MAAM,eAAe,MAAM,YAAY,IAAI;oBAC3C,YAAY,aAAa,QAAQ,IAAI,EAAE;gBACzC;gBAEA,+BAA+B;gBAC/B,MAAM,aAAa,MAAM,MAAM;gBAC/B,IAAI,WAAW,EAAE,EAAE;oBACjB,MAAM,cAAc,MAAM,WAAW,IAAI;oBACzC,eAAe,YAAY,WAAW,IAAI,EAAE;gBAC9C;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO,WAAmB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW;gBAAY;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,CAAA,OAAQ;wBAAC,KAAK,SAAS;2BAAK;qBAAK;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,EAAE;gBAChE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,UAAU,KAAK,SAAS,GAAG;YAG9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,iQAAC;YAAI,WAAU;sBACb,cAAA,iQAAC,2JAAI;gBAAC,WAAU;0BACd,cAAA,iQAAC;oBAAI,WAAU;;sCACb,iQAAC;4BAAI,WAAU;;;;;;sCACf,iQAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,iQAAC;QAAI,WAAU;;0BAEb,iQAAC;gBAAgB,UAAU;gBAAU,SAAS;;;;;;YAG7C,YAAY,GAAG,CAAC,CAAC,sBAChB,iQAAC;oBAA4B,OAAO;oBAAO,QAAQ;mBAAhC,MAAM,EAAE;;;;;YAI5B,YAAY,MAAM,KAAK,mBACtB,iQAAC;gBAAI,WAAU;;kCACb,iQAAC;kCAAE;;;;;;kCACH,iQAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKjC", "debugId": null}}]}