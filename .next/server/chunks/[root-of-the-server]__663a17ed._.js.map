{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/database.ts"], "sourcesContent": ["import sqlite3 from 'sqlite3'\nimport { promisify } from 'util'\nimport path from 'path'\n\n// Types pour notre base de données\nexport interface Client {\n  id: number\n  name: string\n  email?: string\n  hourlyRate?: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Project {\n  id: number\n  name: string\n  description?: string\n  clientId: number\n  hourlyRate?: number\n  isActive: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TimeEntry {\n  id: number\n  projectId: number\n  description?: string\n  startTime: string\n  endTime?: string\n  duration?: number // en minutes\n  isRunning: boolean\n  userId: string\n  createdAt: string\n  updatedAt: string\n}\n\nclass Database {\n  private db: sqlite3.Database | null = null\n\n  async connect() {\n    if (this.db) return this.db\n\n    const dbPath = path.join(process.cwd(), 'data', 'trackify.db')\n    \n    // Créer le dossier data s'il n'existe pas\n    const fs = require('fs')\n    const dataDir = path.dirname(dbPath)\n    if (!fs.existsSync(dataDir)) {\n      fs.mkdirSync(dataDir, { recursive: true })\n    }\n\n    this.db = new sqlite3.Database(dbPath)\n    \n    // Promisifier les méthodes\n    const run = promisify(this.db.run.bind(this.db))\n    const get = promisify(this.db.get.bind(this.db))\n    const all = promisify(this.db.all.bind(this.db))\n\n    // Créer les tables\n    await this.createTables()\n    \n    return this.db\n  }\n\n  private async createTables() {\n    if (!this.db) throw new Error('Database not connected')\n\n    const run = promisify(this.db.run.bind(this.db))\n\n    // Table clients\n    await run(`\n      CREATE TABLE IF NOT EXISTS clients (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL,\n        email TEXT,\n        hourlyRate REAL,\n        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `)\n\n    // Table projets\n    await run(`\n      CREATE TABLE IF NOT EXISTS projects (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL,\n        description TEXT,\n        clientId INTEGER NOT NULL,\n        hourlyRate REAL,\n        isActive BOOLEAN DEFAULT 1,\n        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (clientId) REFERENCES clients (id)\n      )\n    `)\n\n    // Table entrées de temps\n    await run(`\n      CREATE TABLE IF NOT EXISTS timeEntries (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        projectId INTEGER NOT NULL,\n        description TEXT,\n        startTime DATETIME NOT NULL,\n        endTime DATETIME,\n        duration INTEGER,\n        isRunning BOOLEAN DEFAULT 0,\n        userId TEXT NOT NULL,\n        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (projectId) REFERENCES projects (id)\n      )\n    `)\n\n    // Index pour les performances\n    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_userId ON timeEntries (userId)`)\n    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_projectId ON timeEntries (projectId)`)\n    await run(`CREATE INDEX IF NOT EXISTS idx_timeEntries_startTime ON timeEntries (startTime)`)\n  }\n\n  async query(sql: string, params: any[] = []): Promise<any[]> {\n    await this.connect()\n    if (!this.db) throw new Error('Database not connected')\n\n    const all = promisify(this.db.all.bind(this.db))\n    return all(sql, params)\n  }\n\n  async get(sql: string, params: any[] = []): Promise<any> {\n    await this.connect()\n    if (!this.db) throw new Error('Database not connected')\n\n    const get = promisify(this.db.get.bind(this.db))\n    return get(sql, params)\n  }\n\n  async run(sql: string, params: any[] = []): Promise<{ lastID: number; changes: number }> {\n    await this.connect()\n    if (!this.db) throw new Error('Database not connected')\n\n    return new Promise((resolve, reject) => {\n      this.db!.run(sql, params, function(err) {\n        if (err) {\n          reject(err)\n        } else {\n          resolve({ lastID: this.lastID, changes: this.changes })\n        }\n      })\n    })\n  }\n\n  async close() {\n    if (this.db) {\n      const close = promisify(this.db.close.bind(this.db))\n      await close()\n      this.db = null\n    }\n  }\n}\n\n// Instance singleton\nexport const db = new Database()\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAoCA,MAAM;IACI,KAA8B,KAAI;IAE1C,MAAM,UAAU;QACd,IAAI,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE;QAE3B,MAAM,SAAS,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QAEhD,0CAA0C;QAC1C,MAAM;QACN,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,GAAG,UAAU,CAAC,UAAU;YAC3B,GAAG,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;QAEA,IAAI,CAAC,EAAE,GAAG,IAAI,kHAAO,CAAC,QAAQ,CAAC;QAE/B,2BAA2B;QAC3B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAE9C,mBAAmB;QACnB,MAAM,IAAI,CAAC,YAAY;QAEvB,OAAO,IAAI,CAAC,EAAE;IAChB;IAEA,MAAc,eAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAE9C,gBAAgB;QAChB,MAAM,IAAI,CAAC;;;;;;;;;IASX,CAAC;QAED,gBAAgB;QAChB,MAAM,IAAI,CAAC;;;;;;;;;;;;IAYX,CAAC;QAED,yBAAyB;QACzB,MAAM,IAAI,CAAC;;;;;;;;;;;;;;IAcX,CAAC;QAED,8BAA8B;QAC9B,MAAM,IAAI,CAAC,yEAAyE,CAAC;QACrF,MAAM,IAAI,CAAC,+EAA+E,CAAC;QAC3F,MAAM,IAAI,CAAC,+EAA+E,CAAC;IAC7F;IAEA,MAAM,MAAM,GAAW,EAAE,SAAgB,EAAE,EAAkB;QAC3D,MAAM,IAAI,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,OAAO,IAAI,KAAK;IAClB;IAEA,MAAM,IAAI,GAAW,EAAE,SAAgB,EAAE,EAAgB;QACvD,MAAM,IAAI,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,MAAM,MAAM,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC9C,OAAO,IAAI,KAAK;IAClB;IAEA,MAAM,IAAI,GAAW,EAAE,SAAgB,EAAE,EAAgD;QACvF,MAAM,IAAI,CAAC,OAAO;QAClB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,MAAM;QAE9B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,EAAE,CAAE,GAAG,CAAC,KAAK,QAAQ,SAAS,GAAG;gBACpC,IAAI,KAAK;oBACP,OAAO;gBACT,OAAO;oBACL,QAAQ;wBAAE,QAAQ,IAAI,CAAC,MAAM;wBAAE,SAAS,IAAI,CAAC,OAAO;oBAAC;gBACvD;YACF;QACF;IACF;IAEA,MAAM,QAAQ;QACZ,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,MAAM,QAAQ,IAAA,8GAAS,EAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAClD,MAAM;YACN,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF;AAGO,MAAM,KAAK,IAAI", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/services/projectService.ts"], "sourcesContent": ["import { db, Project } from '../database'\n\nexport class ProjectService {\n  static async getAll(): Promise<Project[]> {\n    return db.query(`\n      SELECT p.*, c.name as clientName \n      FROM projects p \n      LEFT JOIN clients c ON p.clientId = c.id \n      ORDER BY p.name\n    `)\n  }\n\n  static async getById(id: number): Promise<Project | null> {\n    return db.get(`\n      SELECT p.*, c.name as clientName \n      FROM projects p \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE p.id = ?\n    `, [id])\n  }\n\n  static async getByClientId(clientId: number): Promise<Project[]> {\n    return db.query(`\n      SELECT p.*, c.name as clientName \n      FROM projects p \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE p.clientId = ? \n      ORDER BY p.name\n    `, [clientId])\n  }\n\n  static async getActive(): Promise<Project[]> {\n    return db.query(`\n      SELECT p.*, c.name as clientName \n      FROM projects p \n      LEFT JOIN clients c ON p.clientId = c.id \n      WHERE p.isActive = 1 \n      ORDER BY p.name\n    `)\n  }\n\n  static async create(data: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>): Promise<Project> {\n    const result = await db.run(\n      'INSERT INTO projects (name, description, clientId, hourlyRate, isActive) VALUES (?, ?, ?, ?, ?)',\n      [data.name, data.description, data.clientId, data.hourlyRate, data.isActive ? 1 : 0]\n    )\n    \n    const project = await this.getById(result.lastID)\n    if (!project) throw new Error('Failed to create project')\n    return project\n  }\n\n  static async update(id: number, data: Partial<Omit<Project, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Project> {\n    const updates: string[] = []\n    const values: any[] = []\n\n    if (data.name !== undefined) {\n      updates.push('name = ?')\n      values.push(data.name)\n    }\n    if (data.description !== undefined) {\n      updates.push('description = ?')\n      values.push(data.description)\n    }\n    if (data.clientId !== undefined) {\n      updates.push('clientId = ?')\n      values.push(data.clientId)\n    }\n    if (data.hourlyRate !== undefined) {\n      updates.push('hourlyRate = ?')\n      values.push(data.hourlyRate)\n    }\n    if (data.isActive !== undefined) {\n      updates.push('isActive = ?')\n      values.push(data.isActive ? 1 : 0)\n    }\n\n    if (updates.length === 0) {\n      const project = await this.getById(id)\n      if (!project) throw new Error('Project not found')\n      return project\n    }\n\n    updates.push('updatedAt = CURRENT_TIMESTAMP')\n    values.push(id)\n\n    await db.run(\n      `UPDATE projects SET ${updates.join(', ')} WHERE id = ?`,\n      values\n    )\n\n    const project = await this.getById(id)\n    if (!project) throw new Error('Project not found')\n    return project\n  }\n\n  static async delete(id: number): Promise<void> {\n    await db.run('DELETE FROM projects WHERE id = ?', [id])\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM;IACX,aAAa,SAA6B;QACxC,OAAO,iJAAE,CAAC,KAAK,CAAC,CAAC;;;;;IAKjB,CAAC;IACH;IAEA,aAAa,QAAQ,EAAU,EAA2B;QACxD,OAAO,iJAAE,CAAC,GAAG,CAAC,CAAC;;;;;IAKf,CAAC,EAAE;YAAC;SAAG;IACT;IAEA,aAAa,cAAc,QAAgB,EAAsB;QAC/D,OAAO,iJAAE,CAAC,KAAK,CAAC,CAAC;;;;;;IAMjB,CAAC,EAAE;YAAC;SAAS;IACf;IAEA,aAAa,YAAgC;QAC3C,OAAO,iJAAE,CAAC,KAAK,CAAC,CAAC;;;;;;IAMjB,CAAC;IACH;IAEA,aAAa,OAAO,IAAqD,EAAoB;QAC3F,MAAM,SAAS,MAAM,iJAAE,CAAC,GAAG,CACzB,mGACA;YAAC,KAAK,IAAI;YAAE,KAAK,WAAW;YAAE,KAAK,QAAQ;YAAE,KAAK,UAAU;YAAE,KAAK,QAAQ,GAAG,IAAI;SAAE;QAGtF,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,MAAM;QAChD,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAC9B,OAAO;IACT;IAEA,aAAa,OAAO,EAAU,EAAE,IAA8D,EAAoB;QAChH,MAAM,UAAoB,EAAE;QAC5B,MAAM,SAAgB,EAAE;QAExB,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;QACA,IAAI,KAAK,WAAW,KAAK,WAAW;YAClC,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,WAAW;QAC9B;QACA,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC/B,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,QAAQ;QAC3B;QACA,IAAI,KAAK,UAAU,KAAK,WAAW;YACjC,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B;QACA,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC/B,QAAQ,IAAI,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,QAAQ,GAAG,IAAI;QAClC;QAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;YAC9B,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC;QACb,OAAO,IAAI,CAAC;QAEZ,MAAM,iJAAE,CAAC,GAAG,CACV,CAAC,oBAAoB,EAAE,QAAQ,IAAI,CAAC,MAAM,aAAa,CAAC,EACxD;QAGF,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC;QACnC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAC9B,OAAO;IACT;IAEA,aAAa,OAAO,EAAU,EAAiB;QAC7C,MAAM,iJAAE,CAAC,GAAG,CAAC,qCAAqC;YAAC;SAAG;IACxD;AACF", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface CloudronUser {\n  id: string\n  username: string\n  email: string\n  displayName: string\n  groups: string[]\n}\n\n/**\n * Extrait les informations utilisateur des headers Cloudron\n */\nexport function getCloudronUser(request: NextRequest): CloudronUser | null {\n  try {\n    // Headers Cloudron standards\n    const userId = request.headers.get('x-cloudron-user-id')\n    const username = request.headers.get('x-cloudron-username')\n    const email = request.headers.get('x-cloudron-user-email')\n    const displayName = request.headers.get('x-cloudron-user-displayname')\n    const groups = request.headers.get('x-cloudron-user-groups')\n\n    if (!userId || !username || !email) {\n      return null\n    }\n\n    return {\n      id: userId,\n      username,\n      email,\n      displayName: displayName || username,\n      groups: groups ? groups.split(',').map(g => g.trim()) : []\n    }\n  } catch (error) {\n    console.error('Erreur lors de l\\'extraction des informations utilisateur Cloudron:', error)\n    return null\n  }\n}\n\n/**\n * Vérifie si l'utilisateur est authentifié via Cloudron\n */\nexport function isAuthenticated(request: NextRequest): boolean {\n  const user = getCloudronUser(request)\n  return user !== null\n}\n\n/**\n * Middleware pour vérifier l'authentification Cloudron\n */\nexport function requireAuth(request: NextRequest): CloudronUser {\n  const user = getCloudronUser(request)\n  \n  if (!user) {\n    throw new Error('Utilisateur non authentifié')\n  }\n  \n  return user\n}\n\n/**\n * Utilitaire pour le développement local (simulation des headers Cloudron)\n */\nexport function createMockCloudronHeaders(): Record<string, string> {\n  if (process.env.NODE_ENV === 'development') {\n    return {\n      'x-cloudron-user-id': 'dev-user-1',\n      'x-cloudron-username': 'developer',\n      'x-cloudron-user-email': '<EMAIL>',\n      'x-cloudron-user-displayname': 'Developer User',\n      'x-cloudron-user-groups': 'admin,users'\n    }\n  }\n  return {}\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAaO,SAAS,gBAAgB,OAAoB;IAClD,IAAI;QACF,6BAA6B;QAC7B,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;QACrC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;QAClC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO;YAClC,OAAO;QACT;QAEA,OAAO;YACL,IAAI;YACJ;YACA;YACA,aAAa,eAAe;YAC5B,QAAQ,SAAS,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM,EAAE;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uEAAuE;QACrF,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,OAAO,gBAAgB;IAC7B,OAAO,SAAS;AAClB;AAKO,SAAS,YAAY,OAAoB;IAC9C,MAAM,OAAO,gBAAgB;IAE7B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAKO,SAAS;IACd,wCAA4C;QAC1C,OAAO;YACL,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,+BAA+B;YAC/B,0BAA0B;QAC5B;IACF;;;AAEF", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { ProjectService } from '@/lib/services/projectService'\nimport { getCloudronUser } from '@/lib/auth'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const user = getCloudronUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })\n    }\n\n    const projects = await ProjectService.getAll()\n    return NextResponse.json({ projects })\n  } catch (error) {\n    console.error('Erreur API projects GET:', error)\n    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const user = getCloudronUser(request)\n    if (!user) {\n      return NextResponse.json({ error: 'Non authentifié' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const { name, description, clientId, hourlyRate, isActive = true } = body\n\n    if (!name || !clientId) {\n      return NextResponse.json({ \n        error: 'Le nom et le client sont requis' \n      }, { status: 400 })\n    }\n\n    const project = await ProjectService.create({\n      name,\n      description,\n      clientId: parseInt(clientId),\n      hourlyRate: hourlyRate ? parseFloat(hourlyRate) : undefined,\n      isActive\n    })\n\n    return NextResponse.json({ project }, { status: 201 })\n  } catch (error) {\n    console.error('Erreur API projects POST:', error)\n    return NextResponse.json({ error: 'Erreur serveur' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,IAAA,0JAAe,EAAC;QAC7B,IAAI,CAAC,MAAM;YACT,OAAO,mKAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,MAAM,WAAW,MAAM,+KAAc,CAAC,MAAM;QAC5C,OAAO,mKAAY,CAAC,IAAI,CAAC;YAAE;QAAS;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,mKAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,IAAA,0JAAe,EAAC;QAC7B,IAAI,CAAC,MAAM;YACT,OAAO,mKAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,IAAI,EAAE,GAAG;QAErE,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB,OAAO,mKAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,UAAU,MAAM,+KAAc,CAAC,MAAM,CAAC;YAC1C;YACA;YACA,UAAU,SAAS;YACnB,YAAY,aAAa,WAAW,cAAc;YAClD;QACF;QAEA,OAAO,mKAAY,CAAC,IAAI,CAAC;YAAE;QAAQ,GAAG;YAAE,QAAQ;QAAI;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,mKAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}