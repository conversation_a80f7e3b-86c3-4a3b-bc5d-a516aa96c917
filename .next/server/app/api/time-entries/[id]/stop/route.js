var R=require("../../../../../chunks/[turbopack]_runtime.js")("server/app/api/time-entries/[id]/stop/route.js")
R.c("server/chunks/ccae0_next_fa8139e0._.js")
R.c("server/chunks/[root-of-the-server]__5878fc28._.js")
R.m("[project]/git/trackify/.next-internal/server/app/api/time-entries/[id]/stop/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/git/trackify/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/git/trackify/src/app/api/time-entries/[id]/stop/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/git/trackify/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/git/trackify/src/app/api/time-entries/[id]/stop/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
