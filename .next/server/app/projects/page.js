var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/projects/page.js")
R.c("server/chunks/ssr/ccae0_c6669222._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/git_trackify_src_app_c344f63c._.js")
R.c("server/chunks/ssr/[root-of-the-server]__346e2ba8._.js")
R.c("server/chunks/ssr/ccae0_next_dist_client_components_e2ce174e._.js")
R.c("server/chunks/ssr/ccae0_next_dist_client_components_builtin_forbidden_64353c3c.js")
R.c("server/chunks/ssr/ccae0_next_dist_client_components_builtin_unauthorized_d1317c00.js")
R.c("server/chunks/ssr/ccae0_next_dist_client_components_builtin_global-error_9d139ef4.js")
R.c("server/chunks/ssr/ccae0_566eadf7._.js")
R.c("server/chunks/ssr/[root-of-the-server]__78c59320._.js")
R.m("[project]/git/trackify/.next-internal/server/app/projects/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/git/trackify/node_modules/next/dist/esm/build/templates/app-page.js?page=/projects/page { GLOBAL_ERROR_MODULE => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/git/trackify/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/git/trackify/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/git/trackify/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/git/trackify/src/app/projects/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/git/trackify/node_modules/next/dist/esm/build/templates/app-page.js?page=/projects/page { GLOBAL_ERROR_MODULE => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/git/trackify/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/git/trackify/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/git/trackify/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/git/trackify/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/git/trackify/src/app/projects/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
