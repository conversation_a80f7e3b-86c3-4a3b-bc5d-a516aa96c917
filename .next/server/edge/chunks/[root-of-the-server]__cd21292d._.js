(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push(["chunks/[root-of-the-server]__cd21292d._.js",
"[externals]/node:buffer [external] (node:buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}),
"[project]/git/trackify/src/lib/auth.ts [middleware-edge] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "createMockCloudronHeaders",
    ()=>createMockCloudronHeaders,
    "getCloudronUser",
    ()=>getCloudronUser,
    "isAuthenticated",
    ()=>isAuthenticated,
    "requireAuth",
    ()=>requireAuth
]);
function getCloudronUser(request) {
    try {
        // Headers Cloudron standards
        const userId = request.headers.get('x-cloudron-user-id');
        const username = request.headers.get('x-cloudron-username');
        const email = request.headers.get('x-cloudron-user-email');
        const displayName = request.headers.get('x-cloudron-user-displayname');
        const groups = request.headers.get('x-cloudron-user-groups');
        if (!userId || !username || !email) {
            return null;
        }
        return {
            id: userId,
            username,
            email,
            displayName: displayName || username,
            groups: groups ? groups.split(',').map((g)=>g.trim()) : []
        };
    } catch (error) {
        console.error('Erreur lors de l\'extraction des informations utilisateur Cloudron:', error);
        return null;
    }
}
function isAuthenticated(request) {
    const user = getCloudronUser(request);
    return user !== null;
}
function requireAuth(request) {
    const user = getCloudronUser(request);
    if (!user) {
        throw new Error('Utilisateur non authentifié');
    }
    return user;
}
function createMockCloudronHeaders() {
    if ("TURBOPACK compile-time truthy", 1) {
        return {
            'x-cloudron-user-id': 'dev-user-1',
            'x-cloudron-username': 'developer',
            'x-cloudron-user-email': '<EMAIL>',
            'x-cloudron-user-displayname': 'Developer User',
            'x-cloudron-user-groups': 'admin,users'
        };
    }
    //TURBOPACK unreachable
    ;
}
}),
"[project]/git/trackify/src/middleware.ts [middleware-edge] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "config",
    ()=>config,
    "middleware",
    ()=>middleware
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/git/trackify/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/node_modules/next/dist/esm/server/web/exports/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/git/trackify/src/lib/auth.ts [middleware-edge] (ecmascript)");
;
;
function middleware(request) {
    // En développement, ajouter les headers mock Cloudron
    if ("TURBOPACK compile-time truthy", 1) {
        const mockHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$src$2f$lib$2f$auth$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createMockCloudronHeaders"])();
        const requestHeaders = new Headers(request.headers);
        Object.entries(mockHeaders).forEach(([key, value])=>{
            requestHeaders.set(key, value);
        });
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$git$2f$trackify$2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$exports$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
            request: {
                headers: requestHeaders
            }
        });
        return response;
    }
    //TURBOPACK unreachable
    ;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'
    ]
};
}),
]);

//# sourceMappingURL=%5Broot-of-the-server%5D__cd21292d._.js.map