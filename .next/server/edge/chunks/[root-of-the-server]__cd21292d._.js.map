{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/git/trackify/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface CloudronUser {\n  id: string\n  username: string\n  email: string\n  displayName: string\n  groups: string[]\n}\n\n/**\n * Extrait les informations utilisateur des headers Cloudron\n */\nexport function getCloudronUser(request: NextRequest): CloudronUser | null {\n  try {\n    // Headers Cloudron standards\n    const userId = request.headers.get('x-cloudron-user-id')\n    const username = request.headers.get('x-cloudron-username')\n    const email = request.headers.get('x-cloudron-user-email')\n    const displayName = request.headers.get('x-cloudron-user-displayname')\n    const groups = request.headers.get('x-cloudron-user-groups')\n\n    if (!userId || !username || !email) {\n      return null\n    }\n\n    return {\n      id: userId,\n      username,\n      email,\n      displayName: displayName || username,\n      groups: groups ? groups.split(',').map(g => g.trim()) : []\n    }\n  } catch (error) {\n    console.error('Erreur lors de l\\'extraction des informations utilisateur Cloudron:', error)\n    return null\n  }\n}\n\n/**\n * Vérifie si l'utilisateur est authentifié via Cloudron\n */\nexport function isAuthenticated(request: NextRequest): boolean {\n  const user = getCloudronUser(request)\n  return user !== null\n}\n\n/**\n * Middleware pour vérifier l'authentification Cloudron\n */\nexport function requireAuth(request: NextRequest): CloudronUser {\n  const user = getCloudronUser(request)\n  \n  if (!user) {\n    throw new Error('Utilisateur non authentifié')\n  }\n  \n  return user\n}\n\n/**\n * Utilitaire pour le développement local (simulation des headers Cloudron)\n */\nexport function createMockCloudronHeaders(): Record<string, string> {\n  if (process.env.NODE_ENV === 'development') {\n    return {\n      'x-cloudron-user-id': 'dev-user-1',\n      'x-cloudron-username': 'developer',\n      'x-cloudron-user-email': '<EMAIL>',\n      'x-cloudron-user-displayname': 'Developer User',\n      'x-cloudron-user-groups': 'admin,users'\n    }\n  }\n  return {}\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAaO,SAAS,gBAAgB,OAAoB;IAClD,IAAI;QACF,6BAA6B;QAC7B,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;QACrC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;QAClC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEnC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO;YAClC,OAAO;QACT;QAEA,OAAO;YACL,IAAI;YACJ;YACA;YACA,aAAa,eAAe;YAC5B,QAAQ,SAAS,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,MAAM,EAAE;QAC5D;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uEAAuE;QACrF,OAAO;IACT;AACF;AAKO,SAAS,gBAAgB,OAAoB;IAClD,MAAM,OAAO,gBAAgB;IAC7B,OAAO,SAAS;AAClB;AAKO,SAAS,YAAY,OAAoB;IAC9C,MAAM,OAAO,gBAAgB;IAE7B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAKO,SAAS;IACd,wCAA4C;QAC1C,OAAO;YACL,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,+BAA+B;YAC/B,0BAA0B;QAC5B;IACF;;;AAEF"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/git/trackify/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\nimport { isAuthenticated, createMockCloudronHeaders } from './lib/auth'\n\nexport function middleware(request: NextRequest) {\n  // En développement, ajouter les headers mock Cloudron\n  if (process.env.NODE_ENV === 'development') {\n    const mockHeaders = createMockCloudronHeaders()\n    const requestHeaders = new Headers(request.headers)\n    \n    Object.entries(mockHeaders).forEach(([key, value]) => {\n      requestHeaders.set(key, value)\n    })\n\n    const response = NextResponse.next({\n      request: {\n        headers: requestHeaders,\n      },\n    })\n    \n    return response\n  }\n\n  // En production, vérifier l'authentification Cloudron\n  if (!isAuthenticated(request)) {\n    // Rediriger vers la page de connexion Cloudron ou afficher une erreur\n    return new NextResponse('Non autorisé', { status: 401 })\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAEA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,sDAAsD;IACtD,wCAA4C;QAC1C,MAAM,cAAc,IAAA,0KAAyB;QAC7C,MAAM,iBAAiB,IAAI,QAAQ,QAAQ,OAAO;QAElD,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC/C,eAAe,GAAG,CAAC,KAAK;QAC1B;QAEA,MAAM,WAAW,mNAAY,CAAC,IAAI,CAAC;YACjC,SAAS;gBACP,SAAS;YACX;QACF;QAEA,OAAO;IACT;;;AASF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}