{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/git_trackify_96a8ecd8._.js", "server/edge/chunks/[root-of-the-server]__cd21292d._.js", "server/edge/chunks/turbopack-git_trackify_edge-wrapper_3b5d3667.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lob+55OzjPWwan1zBDdT5JZHJ798oXN7+keDmdCx0z0=", "__NEXT_PREVIEW_MODE_ID": "68f6dbfcd76b37cd3450bc9d40f4d453", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6bc5b455cd93ba0f38a18532d01d03d12b8e4b98a6b271211aa79d058c1dcf17", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1e992828a770d044407961756a6564da85530c4454d63c730be03167d96794fb"}}}, "sortedMiddleware": ["/"], "functions": {}}