// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/dist/lib/metadata/types/metadata-interface.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/clients/page.tsx
{
  const handler = {} as typeof import("../../src/app/clients/page.js")
  handler satisfies AppPageConfig<"/clients">
}

// Validate ../../src/app/dashboard/page.tsx
{
  const handler = {} as typeof import("../../src/app/dashboard/page.js")
  handler satisfies AppPageConfig<"/dashboard">
}

// Validate ../../src/app/page.tsx
{
  const handler = {} as typeof import("../../src/app/page.js")
  handler satisfies AppPageConfig<"/">
}

// Validate ../../src/app/projects/page.tsx
{
  const handler = {} as typeof import("../../src/app/projects/page.js")
  handler satisfies AppPageConfig<"/projects">
}

// Validate ../../src/app/api/auth/user/route.ts
{
  const handler = {} as typeof import("../../src/app/api/auth/user/route.js")
  handler satisfies RouteHandlerConfig<"/api/auth/user">
}

// Validate ../../src/app/api/clients/route.ts
{
  const handler = {} as typeof import("../../src/app/api/clients/route.js")
  handler satisfies RouteHandlerConfig<"/api/clients">
}

// Validate ../../src/app/api/projects/route.ts
{
  const handler = {} as typeof import("../../src/app/api/projects/route.js")
  handler satisfies RouteHandlerConfig<"/api/projects">
}

// Validate ../../src/app/api/time-entries/[id]/stop/route.ts
{
  const handler = {} as typeof import("../../src/app/api/time-entries/[id]/stop/route.js")
  handler satisfies RouteHandlerConfig<"/api/time-entries/[id]/stop">
}

// Validate ../../src/app/api/time-entries/route.ts
{
  const handler = {} as typeof import("../../src/app/api/time-entries/route.js")
  handler satisfies RouteHandlerConfig<"/api/time-entries">
}

// Validate ../../src/app/api/time-entries/start/route.ts
{
  const handler = {} as typeof import("../../src/app/api/time-entries/start/route.js")
  handler satisfies RouteHandlerConfig<"/api/time-entries/start">
}





// Validate ../../src/app/layout.tsx
{
  const handler = {} as typeof import("../../src/app/layout.js")
  handler satisfies LayoutConfig<"/">
}
