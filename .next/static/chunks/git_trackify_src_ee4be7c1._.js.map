{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,4LAAO,EAAC,IAAA,mKAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,6LAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,8LAAI,GAAG;IAE9B,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,gNAAC,gMAAoB;QACnB,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,gNAAC,iMAAqB;QACpB,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAGgC;QAHhC,EACtB,SAAS,EACT,GAAG,OACmD,GAHhC;IAItB,qBACE,gNAAC,oMAAwB;QACvB,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,gNAAC,0MAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,gNAAC,4MAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,gNAAC,6MAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,gNAAC,4MAA4B;kBAC3B,cAAA,gNAAC,6MAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,IAAA,+IAAE,EACX,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,gNAAC,2MAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,gNAAC,0MAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,IAAA,+IAAE,EACX,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,gNAAC,kNAAkC;QACjC,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,gNAAC;gBAAK,WAAU;0BACd,cAAA,gNAAC,mNAAmC;8BAClC,cAAA,gNAAC,2OAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,gNAAC,gNAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,gNAAC,+MAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,gTACA;QAED,GAAG,KAAK;;0BAET,gNAAC;gBAAK,WAAU;0BACd,cAAA,gNAAC,mNAAmC;8BAClC,cAAA,gNAAC,8OAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,gNAAC,2MAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,+IAAE,EACX,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,gNAAC,+MAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,gNAAC,yMAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,gNAAC,gNAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,IAAA,+IAAE,EACX,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,gNAAC,oQAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,gNAAC,gNAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { CloudronUser } from '@/lib/auth'\n\nexport function useAuth() {\n  const [user, setUser] = useState<CloudronUser | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Récupérer les informations utilisateur depuis l'API\n    fetch('/api/auth/user')\n      .then(res => res.json())\n      .then(data => {\n        setUser(data.user || null)\n        setLoading(false)\n      })\n      .catch(error => {\n        console.error('Erreur lors de la récupération de l\\'utilisateur:', error)\n        setUser(null)\n        setLoading(false)\n      })\n  }, [])\n\n  return {\n    user,\n    loading,\n    isAuthenticated: !!user\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AAKO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,4LAAQ,EAAsB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,4LAAQ,EAAC;IAEvC,IAAA,6LAAS;6BAAC;YACR,sDAAsD;YACtD,MAAM,kBACH,IAAI;qCAAC,CAAA,MAAO,IAAI,IAAI;oCACpB,IAAI;qCAAC,CAAA;oBACJ,QAAQ,KAAK,IAAI,IAAI;oBACrB,WAAW;gBACb;oCACC,KAAK;qCAAC,CAAA;oBACL,QAAQ,KAAK,CAAC,qDAAqD;oBACnE,QAAQ;oBACR,WAAW;gBACb;;QACJ;4BAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;AACF;GAxBgB", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { Button } from '@/components/ui/button'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { \n  DropdownMenu, \n  DropdownMenuContent, \n  DropdownMenuItem, \n  DropdownMenuTrigger \n} from '@/components/ui/dropdown-menu'\nimport { useAuth } from '@/hooks/useAuth'\nimport { \n  Clock, \n  BarChart3, \n  FolderOpen, \n  Users, \n  User,\n  LogOut \n} from 'lucide-react'\n\nconst navigationItems = [\n  {\n    name: 'Temps',\n    href: '/',\n    icon: Clock,\n  },\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: BarChart3,\n  },\n  {\n    name: 'Projets',\n    href: '/projects',\n    icon: FolderOpen,\n  },\n  {\n    name: 'Clients',\n    href: '/clients',\n    icon: Users,\n  },\n]\n\nexport function Navigation() {\n  const pathname = usePathname()\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"container flex h-14 items-center\">\n          <div className=\"mr-4 hidden md:flex\">\n            <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n              <Clock className=\"h-6 w-6\" />\n              <span className=\"hidden font-bold sm:inline-block\">Trackify</span>\n            </Link>\n          </div>\n          <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n            <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n              <div className=\"h-8 w-8 rounded-full bg-muted animate-pulse\" />\n            </div>\n          </div>\n        </div>\n      </nav>\n    )\n  }\n\n  return (\n    <nav className=\"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-14 items-center\">\n        <div className=\"mr-4 hidden md:flex\">\n          <Link href=\"/\" className=\"mr-6 flex items-center space-x-2\">\n            <Clock className=\"h-6 w-6\" />\n            <span className=\"hidden font-bold sm:inline-block\">Trackify</span>\n          </Link>\n          <nav className=\"flex items-center space-x-6 text-sm font-medium\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    \"flex items-center space-x-2 transition-colors hover:text-foreground/80\",\n                    pathname === item.href ? \"text-foreground\" : \"text-foreground/60\"\n                  )}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              )\n            })}\n          </nav>\n        </div>\n        <div className=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n          <div className=\"w-full flex-1 md:w-auto md:flex-none\">\n            {user && (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n                    <Avatar className=\"h-8 w-8\">\n                      <AvatarFallback>\n                        {user.displayName.charAt(0).toUpperCase()}\n                      </AvatarFallback>\n                    </Avatar>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n                  <DropdownMenuItem className=\"flex flex-col items-start\">\n                    <div className=\"font-medium\">{user.displayName}</div>\n                    <div className=\"text-xs text-muted-foreground\">{user.email}</div>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <User className=\"mr-2 h-4 w-4\" />\n                    <span>Profil</span>\n                  </DropdownMenuItem>\n                  <DropdownMenuItem>\n                    <LogOut className=\"mr-2 h-4 w-4\" />\n                    <span>Déconnexion</span>\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            )}\n          </div>\n        </div>\n      </div>\n      \n      {/* Navigation mobile */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-around border-t bg-background p-2\">\n          {navigationItems.map((item) => {\n            const Icon = item.icon\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  \"flex flex-col items-center space-y-1 rounded-md p-2 text-xs transition-colors hover:text-foreground/80\",\n                  pathname === item.href ? \"text-foreground bg-muted\" : \"text-foreground/60\"\n                )}\n              >\n                <Icon className=\"h-5 w-5\" />\n                <span>{item.name}</span>\n              </Link>\n            )\n          })}\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;AAuBA,MAAM,kBAAkB;IACtB;QACE,MAAM;QACN,MAAM;QACN,MAAM,mOAAK;IACb;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qPAAS;IACjB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sPAAU;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mOAAK;IACb;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,IAAA,uKAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,wJAAO;IAEjC,IAAI,SAAS;QACX,qBACE,gNAAC;YAAI,WAAU;sBACb,cAAA,gNAAC;gBAAI,WAAU;;kCACb,gNAAC;wBAAI,WAAU;kCACb,cAAA,gNAAC,6LAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,gNAAC,mOAAK;oCAAC,WAAU;;;;;;8CACjB,gNAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAGvD,gNAAC;wBAAI,WAAU;kCACb,cAAA,gNAAC;4BAAI,WAAU;sCACb,cAAA,gNAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,gNAAC;QAAI,WAAU;;0BACb,gNAAC;gBAAI,WAAU;;kCACb,gNAAC;wBAAI,WAAU;;0CACb,gNAAC,6LAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,gNAAC,mOAAK;wCAAC,WAAU;;;;;;kDACjB,gNAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,gNAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC;oCACpB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,gNAAC,6LAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,IAAA,+IAAE,EACX,0EACA,aAAa,KAAK,IAAI,GAAG,oBAAoB;;0DAG/C,gNAAC;gDAAK,WAAU;;;;;;0DAChB,gNAAC;0DAAM,KAAK,IAAI;;;;;;;uCARX,KAAK,IAAI;;;;;gCAWpB;;;;;;;;;;;;kCAGJ,gNAAC;wBAAI,WAAU;kCACb,cAAA,gNAAC;4BAAI,WAAU;sCACZ,sBACC,gNAAC,kLAAY;;kDACX,gNAAC,yLAAmB;wCAAC,OAAO;kDAC1B,cAAA,gNAAC,kKAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,gNAAC,kKAAM;gDAAC,WAAU;0DAChB,cAAA,gNAAC,0KAAc;8DACZ,KAAK,WAAW,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;kDAK/C,gNAAC,yLAAmB;wCAAC,WAAU;wCAAO,OAAM;wCAAM,UAAU;;0DAC1D,gNAAC,sLAAgB;gDAAC,WAAU;;kEAC1B,gNAAC;wDAAI,WAAU;kEAAe,KAAK,WAAW;;;;;;kEAC9C,gNAAC;wDAAI,WAAU;kEAAiC,KAAK,KAAK;;;;;;;;;;;;0DAE5D,gNAAC,sLAAgB;;kEACf,gNAAC,gOAAI;wDAAC,WAAU;;;;;;kEAChB,gNAAC;kEAAK;;;;;;;;;;;;0DAER,gNAAC,sLAAgB;;kEACf,gNAAC,0OAAM;wDAAC,WAAU;;;;;;kEAClB,gNAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpB,gNAAC;gBAAI,WAAU;0BACb,cAAA,gNAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,gNAAC,6LAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,IAAA,+IAAE,EACX,0GACA,aAAa,KAAK,IAAI,GAAG,6BAA6B;;8CAGxD,gNAAC;oCAAK,WAAU;;;;;;8CAChB,gNAAC;8CAAM,KAAK,IAAI;;;;;;;2BARX,KAAK,IAAI;;;;;oBAWpB;;;;;;;;;;;;;;;;;AAKV;GA3GgB;;QACG,uKAAW;QACF,wJAAO;;;KAFnB", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/AppLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { Navigation } from './Navigation'\n\ninterface AppLayoutProps {\n  children: React.ReactNode\n}\n\nexport function AppLayout({ children }: AppLayoutProps) {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Navigation />\n      <main className=\"container mx-auto py-6\">\n        {children}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAQO,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;IACxB,qBACE,gNAAC;QAAI,WAAU;;0BACb,gNAAC,oKAAU;;;;;0BACX,gNAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;KATgB", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,gNAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,gNAAC;QACC,aAAU;QACV,WAAW,IAAA,+IAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/git/trackify/src/components/TimeTracker.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Card } from '@/components/ui/card'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger\n} from '@/components/ui/dropdown-menu'\nimport {\n  Play,\n  Square,\n  Tag,\n  DollarSign,\n  MoreHorizontal,\n  Plus,\n  Circle\n} from 'lucide-react'\n\n// Types pour les données de l'API\ninterface Project {\n  id: number\n  name: string\n  clientName: string\n  color?: string\n}\n\ninterface TimeEntry {\n  id: number\n  projectId: number\n  projectName: string\n  clientName: string\n  description: string\n  startTime: string\n  endTime?: string\n  duration: number\n  isRunning: boolean\n  isBillable?: boolean\n}\n\n// Couleurs par défaut pour les projets\nconst projectColors = [\n  '#ef4444', '#3b82f6', '#10b981', '#f59e0b',\n  '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'\n]\n\nfunction getProjectColor(projectId: number): string {\n  return projectColors[projectId % projectColors.length]\n}\n\nfunction formatDuration(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const secs = seconds % 60\n  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`\n}\n\nfunction TimeEntryRow({ entry, onStop }: { entry: TimeEntry; onStop: (id: number) => void }) {\n  const [currentDuration, setCurrentDuration] = useState(entry.duration || 0)\n\n  useEffect(() => {\n    if (entry.isRunning) {\n      const interval = setInterval(() => {\n        const startTime = new Date(entry.startTime).getTime()\n        const now = Date.now()\n        const newDuration = Math.floor((now - startTime) / 1000)\n        setCurrentDuration(newDuration)\n      }, 1000)\n\n      return () => clearInterval(interval)\n    }\n  }, [entry.isRunning, entry.startTime])\n\n  const handleStopTimer = () => {\n    if (entry.isRunning) {\n      onStop(entry.id)\n    }\n  }\n\n  return (\n    <Card className=\"p-4\">\n      <div className=\"flex items-center gap-4\">\n        {/* Indicateur de couleur du projet */}\n        <div\n          className=\"w-3 h-3 rounded-full flex-shrink-0\"\n          style={{ backgroundColor: getProjectColor(entry.projectId) }}\n        />\n\n        {/* Nom du projet et client */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center gap-2\">\n            <span className=\"font-medium text-sm\">\n              {entry.projectName}\n            </span>\n            <span className=\"text-xs text-muted-foreground\">\n              - {entry.clientName}\n            </span>\n          </div>\n          <Input\n            placeholder=\"Ajouter une description...\"\n            defaultValue={entry.description}\n            className=\"mt-1 border-none p-0 h-auto text-sm bg-transparent focus-visible:ring-0\"\n          />\n        </div>\n\n        {/* Icône billable */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className={`p-1 h-8 w-8 ${entry.isBillable ? 'text-green-600' : 'text-muted-foreground'}`}\n        >\n          <Tag className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Icône dollar (pour les taux) */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"p-1 h-8 w-8 text-muted-foreground\"\n        >\n          <DollarSign className=\"h-4 w-4\" />\n        </Button>\n\n        {/* Durée */}\n        <div className=\"text-lg font-mono font-medium min-w-[80px] text-right\">\n          {formatDuration(currentDuration)}\n        </div>\n\n        {/* Bouton Start/Stop */}\n        <Button\n          variant={entry.isRunning ? \"destructive\" : \"default\"}\n          size=\"sm\"\n          className=\"min-w-[70px]\"\n          onClick={handleStopTimer}\n        >\n          {entry.isRunning ? (\n            <>\n              <Square className=\"h-4 w-4 mr-1\" />\n              STOP\n            </>\n          ) : (\n            <>\n              <Play className=\"h-4 w-4 mr-1\" />\n              START\n            </>\n          )}\n        </Button>\n\n        {/* Menu options */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" size=\"sm\" className=\"p-1 h-8 w-8\">\n              <MoreHorizontal className=\"h-4 w-4\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\">\n            <DropdownMenuItem>Modifier</DropdownMenuItem>\n            <DropdownMenuItem>Dupliquer</DropdownMenuItem>\n            <DropdownMenuItem className=\"text-destructive\">\n              Supprimer\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </Card>\n  )\n}\n\nfunction NewTimeEntryRow({ projects, onStart }: { projects: Project[]; onStart: (projectId: number, description: string) => void }) {\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null)\n  const [description, setDescription] = useState('')\n\n  const handleStart = () => {\n    if (selectedProject) {\n      onStart(selectedProject.id, description)\n      setDescription('')\n    }\n  }\n\n  return (\n    <Card className=\"p-4 border-dashed\">\n      <div className=\"flex items-center gap-4\">\n        {/* Sélecteur de projet */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"flex items-center gap-2 h-8\">\n              {selectedProject ? (\n                <>\n                  <div\n                    className=\"w-3 h-3 rounded-full\"\n                    style={{ backgroundColor: getProjectColor(selectedProject.id) }}\n                  />\n                  <span className=\"text-sm\">\n                    {selectedProject.name} - {selectedProject.clientName}\n                  </span>\n                </>\n              ) : (\n                <>\n                  <Circle className=\"h-3 w-3\" />\n                  <span className=\"text-sm text-muted-foreground\">\n                    Choisir un projet\n                  </span>\n                </>\n              )}\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-64\">\n            {projects.map((project) => (\n              <DropdownMenuItem\n                key={project.id}\n                onClick={() => setSelectedProject(project)}\n                className=\"flex items-center gap-2\"\n              >\n                <div\n                  className=\"w-3 h-3 rounded-full\"\n                  style={{ backgroundColor: getProjectColor(project.id) }}\n                />\n                <span>{project.name} - {project.clientName}</span>\n              </DropdownMenuItem>\n            ))}\n            <DropdownMenuItem className=\"flex items-center gap-2 text-blue-600\">\n              <Plus className=\"h-3 w-3\" />\n              <span>Nouveau projet</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n\n        {/* Description */}\n        <div className=\"flex-1\">\n          <Input\n            placeholder=\"Que faites-vous ?\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            className=\"border-none p-0 h-auto text-sm bg-transparent focus-visible:ring-0\"\n          />\n        </div>\n\n        {/* Icônes et bouton start */}\n        <Button variant=\"ghost\" size=\"sm\" className=\"p-1 h-8 w-8 text-muted-foreground\">\n          <Tag className=\"h-4 w-4\" />\n        </Button>\n\n        <Button variant=\"ghost\" size=\"sm\" className=\"p-1 h-8 w-8 text-muted-foreground\">\n          <DollarSign className=\"h-4 w-4\" />\n        </Button>\n\n        <div className=\"text-lg font-mono font-medium min-w-[80px] text-right text-muted-foreground\">\n          00:00:00\n        </div>\n\n        <Button\n          disabled={!selectedProject}\n          size=\"sm\"\n          className=\"min-w-[70px]\"\n          onClick={handleStart}\n        >\n          <Play className=\"h-4 w-4 mr-1\" />\n          START\n        </Button>\n\n        <div className=\"w-8\" /> {/* Espace pour aligner avec les autres lignes */}\n      </div>\n    </Card>\n  )\n}\n\nexport function TimeTracker() {\n  const [projects, setProjects] = useState<Project[]>([])\n  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Charger les projets et entrées de temps\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // Charger les projets\n        const projectsRes = await fetch('/api/projects')\n        if (projectsRes.ok) {\n          const projectsData = await projectsRes.json()\n          setProjects(projectsData.projects || [])\n        }\n\n        // Charger les entrées de temps\n        const entriesRes = await fetch('/api/time-entries')\n        if (entriesRes.ok) {\n          const entriesData = await entriesRes.json()\n          setTimeEntries(entriesData.timeEntries || [])\n        }\n      } catch (error) {\n        console.error('Erreur lors du chargement des données:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  const handleStartTimer = async (projectId: number, description: string) => {\n    try {\n      const response = await fetch('/api/time-entries/start', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ projectId, description })\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setTimeEntries(prev => [data.timeEntry, ...prev])\n      }\n    } catch (error) {\n      console.error('Erreur lors du démarrage du timer:', error)\n    }\n  }\n\n  const handleStopTimer = async (entryId: number) => {\n    try {\n      const response = await fetch(`/api/time-entries/${entryId}/stop`, {\n        method: 'POST'\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setTimeEntries(prev =>\n          prev.map(entry =>\n            entry.id === entryId ? data.timeEntry : entry\n          )\n        )\n      }\n    } catch (error) {\n      console.error('Erreur lors de l\\'arrêt du timer:', error)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        <Card className=\"p-4\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-4 bg-muted rounded w-1/4 mb-2\"></div>\n            <div className=\"h-8 bg-muted rounded\"></div>\n          </div>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Nouvelle entrée */}\n      <NewTimeEntryRow projects={projects} onStart={handleStartTimer} />\n\n      {/* Entrées existantes */}\n      {timeEntries.map((entry) => (\n        <TimeEntryRow key={entry.id} entry={entry} onStop={handleStopTimer} />\n      ))}\n\n      {/* Message si aucune entrée */}\n      {timeEntries.length === 0 && (\n        <div className=\"text-center py-8 text-muted-foreground\">\n          <p>Aucune entrée de temps aujourd'hui.</p>\n          <p className=\"text-sm\">Commencez par sélectionner un projet et démarrer le timer !</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;AA4CA,uCAAuC;AACvC,MAAM,gBAAgB;IACpB;IAAW;IAAW;IAAW;IACjC;IAAW;IAAW;IAAW;CAClC;AAED,SAAS,gBAAgB,SAAiB;IACxC,OAAO,aAAa,CAAC,YAAY,cAAc,MAAM,CAAC;AACxD;AAEA,SAAS,eAAe,OAAe;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,OAAO,UAAU;IACvB,OAAO,AAAC,GAAuC,OAArC,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAK,KAA0C,OAAvC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;AACpH;AAEA,SAAS,aAAa,KAAqE;QAArE,EAAE,KAAK,EAAE,MAAM,EAAsD,GAArE;;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,4LAAQ,EAAC,MAAM,QAAQ,IAAI;IAEzE,IAAA,6LAAS;kCAAC;YACR,IAAI,MAAM,SAAS,EAAE;gBACnB,MAAM,WAAW;uDAAY;wBAC3B,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS,EAAE,OAAO;wBACnD,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,MAAM,SAAS,IAAI;wBACnD,mBAAmB;oBACrB;sDAAG;gBAEH;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC,MAAM,SAAS;QAAE,MAAM,SAAS;KAAC;IAErC,MAAM,kBAAkB;QACtB,IAAI,MAAM,SAAS,EAAE;YACnB,OAAO,MAAM,EAAE;QACjB;IACF;IAEA,qBACE,gNAAC,8JAAI;QAAC,WAAU;kBACd,cAAA,gNAAC;YAAI,WAAU;;8BAEb,gNAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,gBAAgB,MAAM,SAAS;oBAAE;;;;;;8BAI7D,gNAAC;oBAAI,WAAU;;sCACb,gNAAC;4BAAI,WAAU;;8CACb,gNAAC;oCAAK,WAAU;8CACb,MAAM,WAAW;;;;;;8CAEpB,gNAAC;oCAAK,WAAU;;wCAAgC;wCAC3C,MAAM,UAAU;;;;;;;;;;;;;sCAGvB,gNAAC,gKAAK;4BACJ,aAAY;4BACZ,cAAc,MAAM,WAAW;4BAC/B,WAAU;;;;;;;;;;;;8BAKd,gNAAC,kKAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAW,AAAC,eAA4E,OAA9D,MAAM,UAAU,GAAG,mBAAmB;8BAEhE,cAAA,gNAAC,6NAAG;wBAAC,WAAU;;;;;;;;;;;8BAIjB,gNAAC,kKAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;8BAEV,cAAA,gNAAC,sPAAU;wBAAC,WAAU;;;;;;;;;;;8BAIxB,gNAAC;oBAAI,WAAU;8BACZ,eAAe;;;;;;8BAIlB,gNAAC,kKAAM;oBACL,SAAS,MAAM,SAAS,GAAG,gBAAgB;oBAC3C,MAAK;oBACL,WAAU;oBACV,SAAS;8BAER,MAAM,SAAS,iBACd;;0CACE,gNAAC,sOAAM;gCAAC,WAAU;;;;;;4BAAiB;;qDAIrC;;0CACE,gNAAC,gOAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;8BAOvC,gNAAC,kLAAY;;sCACX,gNAAC,yLAAmB;4BAAC,OAAO;sCAC1B,cAAA,gNAAC,kKAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,gNAAC,wPAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG9B,gNAAC,yLAAmB;4BAAC,OAAM;;8CACzB,gNAAC,sLAAgB;8CAAC;;;;;;8CAClB,gNAAC,sLAAgB;8CAAC;;;;;;8CAClB,gNAAC,sLAAgB;oCAAC,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GA7GS;KAAA;AA+GT,SAAS,gBAAgB,KAAyG;QAAzG,EAAE,QAAQ,EAAE,OAAO,EAAsF,GAAzG;;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,4LAAQ,EAAiB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,4LAAQ,EAAC;IAE/C,MAAM,cAAc;QAClB,IAAI,iBAAiB;YACnB,QAAQ,gBAAgB,EAAE,EAAE;YAC5B,eAAe;QACjB;IACF;IAEA,qBACE,gNAAC,8JAAI;QAAC,WAAU;kBACd,cAAA,gNAAC;YAAI,WAAU;;8BAEb,gNAAC,kLAAY;;sCACX,gNAAC,yLAAmB;4BAAC,OAAO;sCAC1B,cAAA,gNAAC,kKAAM;gCAAC,SAAQ;gCAAQ,WAAU;0CAC/B,gCACC;;sDACE,gNAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,gBAAgB,gBAAgB,EAAE;4CAAE;;;;;;sDAEhE,gNAAC;4CAAK,WAAU;;gDACb,gBAAgB,IAAI;gDAAC;gDAAI,gBAAgB,UAAU;;;;;;;;iEAIxD;;sDACE,gNAAC,sOAAM;4CAAC,WAAU;;;;;;sDAClB,gNAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAOxD,gNAAC,yLAAmB;4BAAC,WAAU;;gCAC5B,SAAS,GAAG,CAAC,CAAC,wBACb,gNAAC,sLAAgB;wCAEf,SAAS,IAAM,mBAAmB;wCAClC,WAAU;;0DAEV,gNAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,gBAAgB,QAAQ,EAAE;gDAAE;;;;;;0DAExD,gNAAC;;oDAAM,QAAQ,IAAI;oDAAC;oDAAI,QAAQ,UAAU;;;;;;;;uCARrC,QAAQ,EAAE;;;;;8CAWnB,gNAAC,sLAAgB;oCAAC,WAAU;;sDAC1B,gNAAC,gOAAI;4CAAC,WAAU;;;;;;sDAChB,gNAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,gNAAC;oBAAI,WAAU;8BACb,cAAA,gNAAC,gKAAK;wBACJ,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,WAAU;;;;;;;;;;;8BAKd,gNAAC,kKAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;8BAC1C,cAAA,gNAAC,6NAAG;wBAAC,WAAU;;;;;;;;;;;8BAGjB,gNAAC,kKAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;8BAC1C,cAAA,gNAAC,sPAAU;wBAAC,WAAU;;;;;;;;;;;8BAGxB,gNAAC;oBAAI,WAAU;8BAA8E;;;;;;8BAI7F,gNAAC,kKAAM;oBACL,UAAU,CAAC;oBACX,MAAK;oBACL,WAAU;oBACV,SAAS;;sCAET,gNAAC,gOAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAInC,gNAAC;oBAAI,WAAU;;;;;;gBAAQ;;;;;;;;;;;;AAI/B;IAhGS;MAAA;AAkGF,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,4LAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,4LAAQ,EAAc,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,4LAAQ,EAAC;IAEvC,0CAA0C;IAC1C,IAAA,6LAAS;iCAAC;YACR,MAAM;kDAAW;oBACf,IAAI;wBACF,sBAAsB;wBACtB,MAAM,cAAc,MAAM,MAAM;wBAChC,IAAI,YAAY,EAAE,EAAE;4BAClB,MAAM,eAAe,MAAM,YAAY,IAAI;4BAC3C,YAAY,aAAa,QAAQ,IAAI,EAAE;wBACzC;wBAEA,+BAA+B;wBAC/B,MAAM,aAAa,MAAM,MAAM;wBAC/B,IAAI,WAAW,EAAE,EAAE;4BACjB,MAAM,cAAc,MAAM,WAAW,IAAI;4BACzC,eAAe,YAAY,WAAW,IAAI,EAAE;wBAC9C;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0CAA0C;oBAC1D,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO,WAAmB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAW;gBAAY;YAChD;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,CAAA,OAAQ;wBAAC,KAAK,SAAS;2BAAK;qBAAK;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,qBAA4B,OAAR,SAAQ,UAAQ;gBAChE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,UAAU,KAAK,SAAS,GAAG;YAG9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,gNAAC;YAAI,WAAU;sBACb,cAAA,gNAAC,8JAAI;gBAAC,WAAU;0BACd,cAAA,gNAAC;oBAAI,WAAU;;sCACb,gNAAC;4BAAI,WAAU;;;;;;sCACf,gNAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,gNAAC;QAAI,WAAU;;0BAEb,gNAAC;gBAAgB,UAAU;gBAAU,SAAS;;;;;;YAG7C,YAAY,GAAG,CAAC,CAAC,sBAChB,gNAAC;oBAA4B,OAAO;oBAAO,QAAQ;mBAAhC,MAAM,EAAE;;;;;YAI5B,YAAY,MAAM,KAAK,mBACtB,gNAAC;gBAAI,WAAU;;kCACb,gNAAC;kCAAE;;;;;;kCACH,gNAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKjC;IApGgB;MAAA", "debugId": null}}]}