// Script d'initialisation de la base de données
const sqlite3 = require('sqlite3')
const path = require('path')
const fs = require('fs')

// Créer une instance de base de données simple
const dbPath = path.join(__dirname, '..', 'data', 'trackify.db')
const dataDir = path.dirname(dbPath)

// Créer le dossier data s'il n'existe pas
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true })
}

const db = new sqlite3.Database(dbPath)

function initDatabase() {
  return new Promise((resolve, reject) => {
    console.log('Initialisation de la base de données...')

    // Créer les tables d'abord
    db.serialize(() => {
      // Table clients
      db.run(`
        CREATE TABLE IF NOT EXISTS clients (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT,
          hourlyRate REAL,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // Table projets
      db.run(`
        CREATE TABLE IF NOT EXISTS projects (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT,
          clientId INTEGER NOT NULL,
          hourlyRate REAL,
          isActive BOOLEAN DEFAULT 1,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (clientId) REFERENCES clients (id)
        )
      `)

      // Table entrées de temps
      db.run(`
        CREATE TABLE IF NOT EXISTS timeEntries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          projectId INTEGER NOT NULL,
          description TEXT,
          startTime DATETIME NOT NULL,
          endTime DATETIME,
          duration INTEGER,
          isRunning BOOLEAN DEFAULT 0,
          userId TEXT NOT NULL,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (projectId) REFERENCES projects (id)
        )
      `)

      // Créer quelques clients de test
      console.log('Création des clients de test...')
      db.run(
        'INSERT INTO clients (name, email, hourlyRate) VALUES (?, ?, ?)',
        ['Kapreon', '<EMAIL>', 75.0],
        function(err) {
          if (err) {
            console.error('Erreur client 1:', err)
            return reject(err)
          }
          const client1Id = this.lastID

          db.run(
            'INSERT INTO clients (name, email, hourlyRate) VALUES (?, ?, ?)',
            ['TechCorp', '<EMAIL>', 85.0],
            function(err) {
              if (err) {
                console.error('Erreur client 2:', err)
                return reject(err)
              }
              const client2Id = this.lastID

              db.run(
                'INSERT INTO clients (name, email, hourlyRate) VALUES (?, ?, ?)',
                ['StartupXYZ', '<EMAIL>', 65.0],
                function(err) {
                  if (err) {
                    console.error('Erreur client 3:', err)
                    return reject(err)
                  }
                  const client3Id = this.lastID

                  // Créer quelques projets de test
                  console.log('Création des projets de test...')
                  db.run(
                    'INSERT INTO projects (name, description, clientId, hourlyRate, isActive) VALUES (?, ?, ?, ?, ?)',
                    ['Comptabilité', 'Gestion comptable et facturation', client1Id, 75.0, 1]
                  )

                  db.run(
                    'INSERT INTO projects (name, description, clientId, hourlyRate, isActive) VALUES (?, ?, ?, ?, ?)',
                    ['Développement Web', 'Création du site web corporate', client2Id, 85.0, 1]
                  )

                  db.run(
                    'INSERT INTO projects (name, description, clientId, hourlyRate, isActive) VALUES (?, ?, ?, ?, ?)',
                    ['Design UI/UX', 'Refonte de l\'interface utilisateur', client3Id, 65.0, 1]
                  )

                  db.run(
                    'INSERT INTO projects (name, description, clientId, hourlyRate, isActive) VALUES (?, ?, ?, ?, ?)',
                    ['Maintenance', 'Maintenance et support technique', client2Id, 60.0, 1],
                    function(err) {
                      if (err) {
                        console.error('Erreur projet:', err)
                        return reject(err)
                      }

                      console.log('Base de données initialisée avec succès !')
                      console.log('Clients créés:', client1Id, client2Id, client3Id)

                      // Fermer la connexion
                      db.close((err) => {
                        if (err) {
                          console.error('Erreur fermeture DB:', err)
                          return reject(err)
                        }
                        resolve()
                      })
                    }
                  )
                }
              )
            }
          )
        }
      )
    })
  })
}

// Exécuter le script
initDatabase()
  .then(() => {
    console.log('Script terminé avec succès')
    process.exit(0)
  })
  .catch((error) => {
    console.error('Erreur lors de l\'initialisation:', error)
    process.exit(1)
  })
